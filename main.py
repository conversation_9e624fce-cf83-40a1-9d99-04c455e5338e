# main.py

import os
from datetime import datetime, timedelta
from dotenv import load_dotenv
import anthropic
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
import pickle
import sys
import base64
import psycopg2
from psycopg2.extras import RealDictCursor
import json
from prompts import EXTRACT_NEWS_ITEMS_PROMPT, SCORE_NEWS_ITEM_PROMPT, ENHANCE_NEWS_ITEM_PROMPT

load_dotenv()

SCOPES = ['https://www.googleapis.com/auth/gmail.readonly', 'https://www.googleapis.com/auth/gmail.modify', 'https://www.googleapis.com/auth/gmail.send']

def get_db_connection():
    return psycopg2.connect(os.getenv('DATABASE_URL'))

def authenticate_gmail():
    creds = None

    if os.path.exists('token.pickle'):
        with open('token.pickle', 'rb') as token:
            creds = pickle.load(token)

    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file('credentials.json', SCOPES)
            creds = flow.run_local_server(port=0)

        with open('token.pickle', 'wb') as token:
            pickle.dump(creds, token)

    return build('gmail', 'v1', credentials=creds)

def fetch_newsletters(service, hours_back=24):
    """Fetch newsletters from the last 24 hours"""

    # calculate time window
    after_date = (datetime.now() - timedelta(hours=hours_back)).strftime('%Y/%m/%d')

    # build query
    query = f'label:Newsletters after:{after_date}'

    try:
        results = service.users().messages().list(
            userId='me',
            q=query,
            maxResults=25
        ).execute()

        messages = results.get('messages',[])

        # fetch full content for each message 
        newsletters = []
        for msg in messages:
            # get full message
            message = service.users().messages().get(
                userId='me',
                id=msg['id'],
            ).execute()

            # extract useful info
            headers = message['payload'].get('headers',[])
            subject = next((h['value'] for h in headers if h['name'] == 'Subject'), 'No subject')
            sender = next((h['value'] for h in headers if h['name'] == 'From'), 'Unknown Sender')

            # get body
            body = extract_body(message['payload'])

            newsletters.append({
                'id': msg['id'],
                'subject': subject,
                'sender': sender,
                'body': body
            })

        return newsletters
    
    except Exception as e:
        print(f"Error fetching newsletters: {e}")
        raise
    
def extract_body(payload):
    """Extract the body of a message"""
    body = ''

    # gmail sorts emails into parts, e.g. html and plain text
    if 'parts' in payload:
        for part in payload['parts']:
            if part['mimeType'] == 'text/html':
                data = part['body']['data']
                html_body = base64.urlsafe_b64decode(data).decode('utf-8', errors='ignore')
                # html to text conversion
                body = html_body.replace('<br>','\n').replace('</p>','\n')
                # remove html tags
                import re
                body = re.sub('<[^<]+?>','',body)
                return body
        # if no html, try plaint text      
        for part in payload['parts']:
            if part['mimeType'] == 'text/plain':
                data = part['body']['data']
                body += base64.urlsafe_b64decode(data).decode('utf-8', errors='ignore')
    else:
        # not multipart
        if payload['body'].get('data'):
            body = base64.urlsafe_b64decode(
                payload['body']['data']
            ).decode('utf-8', errors='ignore')

    return body

def extract_news_items(newsletter, client):
    """Extract news items from a newsletter"""
    # define system prompt
    prompt = EXTRACT_NEWS_ITEMS_PROMPT.format(
        sender=newsletter['sender'],
        body=newsletter['body']
    )

    response = client.messages.create(
        model="claude-3-5-haiku-latest",
        max_tokens=8000,
        temperature=0,
        messages=[{"role": "user", "content": prompt}]
    )

    # parse items from response
    items_text = response.content[0].text
    items = []
    for item in items_text.split('ITEM_START'):
        if 'ITEM_END' in item:
            content = item.split('ITEM_END')[0].strip()
            if content:
                items.append(content)
    return items

def score_news_item(item, newsletter_source, client):
    """Score a single news item"""

    prompt = SCORE_NEWS_ITEM_PROMPT.format(item=item)

    response = client.messages.create(
        model="claude-3-5-haiku-latest",
        max_tokens=500,
        temperature=0,
        messages=[{"role": "user", "content": prompt}]
    )

    # parse response
    result = {}
    for line in response.content[0].text.strip().split('\n'):
        if ': ' in line:
            key, value = line.split(': ',1)
            result[key] = value
    return {
        'score': float(result.get('SCORE', 0)),
        'category': result.get('CATEGORY', 'Other'),
        'key_entities': [e.strip() for e in result.get('KEY_ENTITIES', '').split(',')],
        'summary': result.get('SUMMARY', '')
    }

def store_news_item(conn, newsletter_source, content, score, category, key_entities, summary):
    """Store a news item in the database"""
    with conn.cursor() as cur:
        cur.execute("""
        INSERT INTO news_items (newsletter_source, newsletter_date, content, score, category, key_entities, summary)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        RETURNING id
        """, (newsletter_source, datetime.now().date(), content, score, category, key_entities, summary))
        return cur.fetchone()[0]

def check_duplicates(conn, key_entities, days_back=7):
    """Check for duplicates in the last X days"""
    with conn.cursor() as cur:
        cur.execute("""
                    SELECT id, key_entities, summary, score
                    FROM news_items
                    WHERE newsletter_date > CURRENT_DATE - INTERVAL '%s days'
                    AND key_entities && %s
                    ORDER BY score DESC
                """, (days_back, key_entities))
        return cur.fetchall()
    
def enhance_news_items(top_items, client):
    enhanced_items = []
    for item in top_items:
        prompt = ENHANCE_NEWS_ITEM_PROMPT.format(
            category=item['category'],
            summary=item['summary']
        )

        response = client.messages.create(
            model="claude-3-5-haiku-latest",
            max_tokens=300,
            temperature=0,
            messages=[{"role": "user", "content": prompt}]
        )
        
        # Parse response
        enhanced = item.copy()
        response_text = response.content[0].text
        
        for line in response_text.split('\n'):
            if line.startswith('WHY_THIS_MATTERS:'):
                enhanced['why_matters'] = line.replace('WHY_THIS_MATTERS:', '').strip()
            elif line.startswith('TECHNICAL_NOTES:'):
                tech_notes = line.replace('TECHNICAL_NOTES:', '').strip()
                enhanced['tech_notes'] = None if tech_notes == "None" else tech_notes
        
        enhanced_items.append(enhanced)
    
    return enhanced_items

def create_digest_html(enhanced_items):
    """Create HTML email from enhanced items"""
    
    html = f"""
    <html>
    <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1>DEEP TECH DIGEST - {datetime.now().strftime('%B %d, %Y')}</h1>
        <p style="color: #666;">Your top {len(enhanced_items)} stories for today</p>
        <hr>
    """
    
    for i, item in enumerate(enhanced_items, 1):
        html += f"""
        <div style="margin-bottom: 30px;">
            <h3>{i}. [{item['score']}/10] {item['summary']}</h3>
            <p style="color: #666; font-size: 14px;">Source: {item['source']} | Category: {item['category']}</p>
            
            <p><strong>💡 Why this matters:</strong> {item['why_matters']}</p>
        """
        
        if item.get('tech_notes'):
            html += f"""<p><strong>🔧 Technical notes:</strong> {item['tech_notes']}</p>"""
            
        html += "</div><hr style='border: none; border-top: 1px solid #eee;'>"
    
    html += """
    </body>
    </html>
    """
    
    return html

def send_email(service, to_email, subject, body):
    """Send an email using Gmail API"""
    import email.mime.text
    import email.mime.multipart

    message = email.mime.multipart.MIMEMultipart()
    message['to'] = to_email
    message['subject'] = subject
    message.attach(email.mime.text.MIMEText(body, 'html'))

    raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()

    try:
        service.users().messages().send(
            userId='me',
            body={'raw': raw_message}
        ).execute()
        print(f"✅ Email sent to {to_email}")
        return True
    
    except Exception as e:
        print(f"❌ Failed to send email: {e}")
        return False
    
def cleanup_old_items(conn, days_to_keep=30):
    """Delete news items older than X days, except those in digest history"""
    with conn.cursor() as cur:
        # Delete old items that weren't included in any digest
        cur.execute("""
            DELETE FROM news_items 
            WHERE newsletter_date < CURRENT_DATE - INTERVAL '%s days'
            AND id NOT IN (
                SELECT UNNEST(news_item_ids) 
                FROM digest_history
            )
        """, (days_to_keep,))
        
        deleted_count = cur.rowcount
        
        cur.execute("""
            DELETE FROM digest_history
            WHERE digest_date < CURRENT_DATE - INTERVAL '%s days'
        """, (days_to_keep * 3,))  # Keep digest history 3x longer
        
        return deleted_count

def main():
    print(f"Newsletter digest running at {datetime.now()}")
    # We'll add Gmail and LLM logic here

    try:
        service = authenticate_gmail()
        print("GREAT SUCCESS connected to gmail")

        conn = get_db_connection()
        print("WOWWWEEEEEE connected to db")

        deleted_count = cleanup_old_items(conn)
        if deleted_count > 0:
            print(f"🧹 Cleaned up {deleted_count} old news items")

        client = anthropic.Anthropic(api_key=os.getenv('ANTHROPIC_API_KEY'))
        print("OHHH MYYYY connected to claude")

        #fetch newsletters
        newsletters = fetch_newsletters(service)
        print(f"\n📧 Found {len(newsletters)} newsletters to process")

        #process each newsletter
        all_items = []
        for newsletter in newsletters:
            items = extract_news_items(newsletter, client)
            print(f" Found {len(items)} items in newsletter from {newsletter['sender']}")

            for item in items:
                scored = score_news_item(item, newsletter['sender'], client)

                duplicates = check_duplicates(conn, scored['key_entities'])

                if not duplicates and scored['score']>=7: #only store significant
                    item_id = store_news_item(
                        conn,
                        newsletter['sender'],
                        item,
                        scored['score'],
                        scored['category'],
                        scored['key_entities'],
                        scored['summary']
                    )
                    print(f"Stored: [{scored['score']}] for {scored['summary']}")
                    
                    all_items.append({
                        'id': item_id,
                        'score': scored['score'],
                        'category': scored['category'],
                        'summary': scored['summary'],
                        'source': newsletter['sender']
                    })
        
        conn.commit()

        top_items = sorted(all_items, key=lambda x: x['score'], reverse=True)[:10]

        if not top_items:
            print("❌ No significant news items found today")
            conn.close()
            sys.exit(0)

        print("🧠 Adding context to top stories...")
        enhanced_items = enhance_news_items(top_items, client)

        digest_html = create_digest_html(enhanced_items)

        your_email = "<EMAIL>"
        subject = f"Deep Tech Digest - {datetime.now().strftime('%B %d, %Y')}"

        if send_email(service, your_email, subject, digest_html):
            print("✅ Email sent successfully")
        
            with conn.cursor() as cur:
                cur.execute("""
                        INSERT INTO digest_history (digest_date, news_item_ids, sent_at)
                        VALUES (%s, %s, %s)
                """, (
                    datetime.now().date(),
                    [item['id'] for item in top_items],
                    datetime.now()
                ))
            conn.commit()

        conn.close()
        sys.exit(0)
    
    except FileNotFoundError as e:
        print(f"❌ Missing file: {e}")
        sys.exit(2)

    except Exception as e:
        print(f"❌ An error occurred: {e}")
        sys.exit(1)
        
    
if __name__ == "__main__":
    main()