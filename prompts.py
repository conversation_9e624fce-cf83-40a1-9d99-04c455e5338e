# prompts.py

"""Prompts for newsletter processing"""

EXTRACT_NEWS_ITEMS_PROMPT = """Extract individual news items from this newsletter. Each item should be:
- Self-contained (can be understood without other context)
- About a single topic (one funding round, one paper, one acquisition, etc.)
- Include the complete relevant information

Return in this format:
ITEM_START
[Complete text about this news item]
ITEM_END

Newsletter from {sender}:
{body}"""

SCORE_NEWS_ITEM_PROMPT = """Score this news item for a deep tech VC who needs to learn about the latest news in the field and globally(0-10):

 Scoring examples:
    - AI breakthroughs like the launch of AlphaCode: 9-10
    - Major funding like more than $50M: 8-9
    - Strategic acquisitions like Meta acquiring Scale AI: 7-8
    - Breakthrough tech like discovery of a new organelle that is relevant for celluar biology research: 8-10
    - Major economic or geopolitical changes like the US-China trade war: 8-9

Item: {item}

Return ONLY:
SCORE: [number]
CATEGORY: [AI/Funding/Acquisition/Breakthrough/Policy/Other]
KEY_ENTITIES: [comma-separated list for deduplication]
SUMMARY: [clear, concise, succinct summary less than 400 words]"""

ENHANCE_NEWS_ITEM_PROMPT = """For this {category} news item, provide:

1. WHY_THIS_MATTERS: 1-2 sentences explaining significance for deep tech/AI venture investing
2. TECHNICAL_NOTES: Only if there are new technical terms/concepts that need explanation. Otherwise write "None"

News item: {summary}

Format your response exactly as:
WHY_THIS_MATTERS: [your text]
TECHNICAL_NOTES: [your text or "None"]"""