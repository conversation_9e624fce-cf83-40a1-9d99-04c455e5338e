{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/analytics.edit": {"description": "Edit Google Analytics management entities"}, "https://www.googleapis.com/auth/analytics.readonly": {"description": "See and download your Google Analytics data"}}}}, "basePath": "", "baseUrl": "https://analyticsadmin.googleapis.com/", "batchPath": "batch", "canonicalName": "Google Analytics Admin", "description": "Manage properties in Google Analytics. Warning: Creating multiple Customer Applications, Accounts, or Projects to simulate or act as a single Customer Application, Account, or Project (respectively) or to circumvent Service-specific usage limits or quotas is a direct violation of Google Cloud Platform Terms of Service as well as Google APIs Terms of Service. These actions can result in immediate termination of your GCP project(s) without any warning.", "discoveryVersion": "v1", "documentationLink": "http://code.google.com/apis/analytics/docs/mgmt/home.html", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "analyticsadmin:v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://analyticsadmin.mtls.googleapis.com/", "name": "analyticsadmin", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accountSummaries": {"methods": {"list": {"description": "Returns summaries of all accounts accessible by the caller.", "flatPath": "v1beta/accountSummaries", "httpMethod": "GET", "id": "analyticsadmin.accountSummaries.list", "parameterOrder": [], "parameters": {"pageSize": {"description": "The maximum number of AccountSummary resources to return. The service may return fewer than this value, even if there are additional pages. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAccountSummaries` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAccountSummaries` must match the call that provided the page token.", "location": "query", "type": "string"}}, "path": "v1beta/accountSummaries", "response": {"$ref": "GoogleAnalyticsAdminV1betaListAccountSummariesResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}}}, "accounts": {"methods": {"delete": {"description": "Marks target Account as soft-deleted (ie: \"trashed\") and returns it. This API does not have a method to restore soft-deleted accounts. However, they can be restored using the Trash Can UI. If the accounts are not restored before the expiration time, the account and all child resources (eg: Properties, GoogleAdsLinks, Streams, AccessBindings) will be permanently purged. https://support.google.com/analytics/answer/6154772 Returns an error if the target is not found.", "flatPath": "v1beta/accounts/{accountsId}", "httpMethod": "DELETE", "id": "analyticsadmin.accounts.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Account to soft-delete. Format: accounts/{account} Example: \"accounts/100\"", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single Account.", "flatPath": "v1beta/accounts/{accountsId}", "httpMethod": "GET", "id": "analyticsadmin.accounts.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the account to lookup. Format: accounts/{account} Example: \"accounts/100\"", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1betaAccount"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "getDataSharingSettings": {"description": "Get data sharing settings on an account. Data sharing settings are singletons.", "flatPath": "v1beta/accounts/{accountsId}/dataSharingSettings", "httpMethod": "GET", "id": "analyticsadmin.accounts.getDataSharingSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the settings to lookup. Format: accounts/{account}/dataSharingSettings Example: `accounts/1000/dataSharingSettings`", "location": "path", "pattern": "^accounts/[^/]+/dataSharingSettings$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1betaDataSharingSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Returns all accounts accessible by the caller. Note that these accounts might not currently have GA properties. Soft-deleted (ie: \"trashed\") accounts are excluded by default. Returns an empty list if no relevant accounts are found.", "flatPath": "v1beta/accounts", "httpMethod": "GET", "id": "analyticsadmin.accounts.list", "parameterOrder": [], "parameters": {"pageSize": {"description": "The maximum number of resources to return. The service may return fewer than this value, even if there are additional pages. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListAccounts` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListAccounts` must match the call that provided the page token.", "location": "query", "type": "string"}, "showDeleted": {"description": "Whether to include soft-deleted (ie: \"trashed\") Accounts in the results. Accounts can be inspected to determine whether they are deleted or not.", "location": "query", "type": "boolean"}}, "path": "v1beta/accounts", "response": {"$ref": "GoogleAnalyticsAdminV1betaListAccountsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates an account.", "flatPath": "v1beta/accounts/{accountsId}", "httpMethod": "PATCH", "id": "analyticsadmin.accounts.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this account. Format: accounts/{account} Example: \"accounts/100\"", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (for example, \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1betaAccount"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaAccount"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "provisionAccountTicket": {"description": "Requests a ticket for creating an account.", "flatPath": "v1beta/accounts:provisionAccountTicket", "httpMethod": "POST", "id": "analyticsadmin.accounts.provisionAccountTicket", "parameterOrder": [], "parameters": {}, "path": "v1beta/accounts:provisionAccountTicket", "request": {"$ref": "GoogleAnalyticsAdminV1betaProvisionAccountTicketRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaProvisionAccountTicketResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "runAccessReport": {"description": "Returns a customized report of data access records. The report provides records of each time a user reads Google Analytics reporting data. Access records are retained for up to 2 years. Data Access Reports can be requested for a property. Reports may be requested for any property, but dimensions that aren't related to quota can only be requested on Google Analytics 360 properties. This method is only available to Administrators. These data access records include GA UI Reporting, GA UI Explorations, GA Data API, and other products like Firebase & Admob that can retrieve data from Google Analytics through a linkage. These records don't include property configuration changes like adding a stream or changing a property's time zone. For configuration change history, see [searchChangeHistoryEvents](https://developers.google.com/analytics/devguides/config/admin/v1/rest/v1alpha/accounts/searchChangeHistoryEvents). To give your feedback on this API, complete the [Google Analytics Access Reports feedback](https://docs.google.com/forms/d/e/1FAIpQLSdmEBUrMzAEdiEKk5TV5dEHvDUZDRlgWYdQdAeSdtR4hVjEhw/viewform) form.", "flatPath": "v1beta/accounts/{accountsId}:runAccessReport", "httpMethod": "POST", "id": "analyticsadmin.accounts.runAccessReport", "parameterOrder": ["entity"], "parameters": {"entity": {"description": "The Data Access Report supports requesting at the property level or account level. If requested at the account level, Data Access Reports include all access for all properties under that account. To request at the property level, entity should be for example 'properties/123' if \"123\" is your Google Analytics property ID. To request at the account level, entity should be for example 'accounts/1234' if \"1234\" is your Google Analytics Account ID.", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+entity}:runAccessReport", "request": {"$ref": "GoogleAnalyticsAdminV1betaRunAccessReportRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaRunAccessReportResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "searchChangeHistoryEvents": {"description": "Searches through all changes to an account or its children given the specified set of filters. Only returns the subset of changes supported by the API. The UI may return additional changes.", "flatPath": "v1beta/accounts/{accountsId}:searchChangeHistoryEvents", "httpMethod": "POST", "id": "analyticsadmin.accounts.searchChangeHistoryEvents", "parameterOrder": ["account"], "parameters": {"account": {"description": "Required. The account resource for which to return change history resources. Format: accounts/{account} Example: `accounts/100`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+account}:searchChangeHistoryEvents", "request": {"$ref": "GoogleAnalyticsAdminV1betaSearchChangeHistoryEventsRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaSearchChangeHistoryEventsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "properties": {"methods": {"acknowledgeUserDataCollection": {"description": "Acknowledges the terms of user data collection for the specified property. This acknowledgement must be completed (either in the Google Analytics UI or through this API) before MeasurementProtocolSecret resources may be created.", "flatPath": "v1beta/properties/{propertiesId}:acknowledgeUserDataCollection", "httpMethod": "POST", "id": "analyticsadmin.properties.acknowledgeUserDataCollection", "parameterOrder": ["property"], "parameters": {"property": {"description": "Required. The property for which to acknowledge user data collection.", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+property}:acknowledgeUserDataCollection", "request": {"$ref": "GoogleAnalyticsAdminV1betaAcknowledgeUserDataCollectionRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaAcknowledgeUserDataCollectionResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "create": {"description": "Creates a Google Analytics property with the specified location and attributes.", "flatPath": "v1beta/properties", "httpMethod": "POST", "id": "analyticsadmin.properties.create", "parameterOrder": [], "parameters": {}, "path": "v1beta/properties", "request": {"$ref": "GoogleAnalyticsAdminV1betaProperty"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaProperty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Marks target Property as soft-deleted (ie: \"trashed\") and returns it. This API does not have a method to restore soft-deleted properties. However, they can be restored using the Trash Can UI. If the properties are not restored before the expiration time, the Property and all child resources (eg: GoogleAdsLinks, Streams, AccessBindings) will be permanently purged. https://support.google.com/analytics/answer/6154772 Returns an error if the target is not found.", "flatPath": "v1beta/properties/{propertiesId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Property to soft-delete. Format: properties/{property_id} Example: \"properties/1000\"", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1betaProperty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single GA Property.", "flatPath": "v1beta/properties/{propertiesId}", "httpMethod": "GET", "id": "analyticsadmin.properties.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the property to lookup. Format: properties/{property_id} Example: \"properties/1000\"", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1betaProperty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "getDataRetentionSettings": {"description": "Returns the singleton data retention settings for this property.", "flatPath": "v1beta/properties/{propertiesId}/dataRetentionSettings", "httpMethod": "GET", "id": "analyticsadmin.properties.getDataRetentionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the settings to lookup. Format: properties/{property}/dataRetentionSettings Example: \"properties/1000/dataRetentionSettings\"", "location": "path", "pattern": "^properties/[^/]+/dataRetentionSettings$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1betaDataRetentionSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Returns child Properties under the specified parent Account. Properties will be excluded if the caller does not have access. Soft-deleted (ie: \"trashed\") properties are excluded by default. Returns an empty list if no relevant properties are found.", "flatPath": "v1beta/properties", "httpMethod": "GET", "id": "analyticsadmin.properties.list", "parameterOrder": [], "parameters": {"filter": {"description": "Required. An expression for filtering the results of the request. Fields eligible for filtering are: `parent:`(The resource name of the parent account/property) or `ancestor:`(The resource name of the parent account) or `firebase_project:`(The id or number of the linked firebase project). Some examples of filters: ``` | Filter | Description | |-----------------------------|-------------------------------------------| | parent:accounts/123 | The account with account id: 123. | | parent:properties/123 | The property with property id: 123. | | ancestor:accounts/123 | The account with account id: 123. | | firebase_project:project-id | The firebase project with id: project-id. | | firebase_project:123 | The firebase project with number: 123. | ```", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of resources to return. The service may return fewer than this value, even if there are additional pages. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListProperties` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListProperties` must match the call that provided the page token.", "location": "query", "type": "string"}, "showDeleted": {"description": "Whether to include soft-deleted (ie: \"trashed\") Properties in the results. Properties can be inspected to determine whether they are deleted or not.", "location": "query", "type": "boolean"}}, "path": "v1beta/properties", "response": {"$ref": "GoogleAnalyticsAdminV1betaListPropertiesResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a property.", "flatPath": "v1beta/properties/{propertiesId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this property. Format: properties/{property_id} Example: \"properties/1000\"", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1betaProperty"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaProperty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "runAccessReport": {"description": "Returns a customized report of data access records. The report provides records of each time a user reads Google Analytics reporting data. Access records are retained for up to 2 years. Data Access Reports can be requested for a property. Reports may be requested for any property, but dimensions that aren't related to quota can only be requested on Google Analytics 360 properties. This method is only available to Administrators. These data access records include GA UI Reporting, GA UI Explorations, GA Data API, and other products like Firebase & Admob that can retrieve data from Google Analytics through a linkage. These records don't include property configuration changes like adding a stream or changing a property's time zone. For configuration change history, see [searchChangeHistoryEvents](https://developers.google.com/analytics/devguides/config/admin/v1/rest/v1alpha/accounts/searchChangeHistoryEvents). To give your feedback on this API, complete the [Google Analytics Access Reports feedback](https://docs.google.com/forms/d/e/1FAIpQLSdmEBUrMzAEdiEKk5TV5dEHvDUZDRlgWYdQdAeSdtR4hVjEhw/viewform) form.", "flatPath": "v1beta/properties/{propertiesId}:runAccessReport", "httpMethod": "POST", "id": "analyticsadmin.properties.runAccessReport", "parameterOrder": ["entity"], "parameters": {"entity": {"description": "The Data Access Report supports requesting at the property level or account level. If requested at the account level, Data Access Reports include all access for all properties under that account. To request at the property level, entity should be for example 'properties/123' if \"123\" is your Google Analytics property ID. To request at the account level, entity should be for example 'accounts/1234' if \"1234\" is your Google Analytics Account ID.", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+entity}:runAccessReport", "request": {"$ref": "GoogleAnalyticsAdminV1betaRunAccessReportRequest"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaRunAccessReportResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "updateDataRetentionSettings": {"description": "Updates the singleton data retention settings for this property.", "flatPath": "v1beta/properties/{propertiesId}/dataRetentionSettings", "httpMethod": "PATCH", "id": "analyticsadmin.properties.updateDataRetentionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name for this DataRetentionSetting resource. Format: properties/{property}/dataRetentionSettings", "location": "path", "pattern": "^properties/[^/]+/dataRetentionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1betaDataRetentionSettings"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaDataRetentionSettings"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}, "resources": {"conversionEvents": {"deprecated": true, "methods": {"create": {"deprecated": true, "description": "Deprecated: Use `CreateKeyEvent` instead. Creates a conversion event with the specified attributes.", "flatPath": "v1beta/properties/{propertiesId}/conversionEvents", "httpMethod": "POST", "id": "analyticsadmin.properties.conversionEvents.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the parent property where this conversion event will be created. Format: properties/123", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/conversionEvents", "request": {"$ref": "GoogleAnalyticsAdminV1betaConversionEvent"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaConversionEvent"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"deprecated": true, "description": "Deprecated: Use `DeleteKeyEvent` instead. Deletes a conversion event in a property.", "flatPath": "v1beta/properties/{propertiesId}/conversionEvents/{conversionEventsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.conversionEvents.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the conversion event to delete. Format: properties/{property}/conversionEvents/{conversion_event} Example: \"properties/123/conversionEvents/456\"", "location": "path", "pattern": "^properties/[^/]+/conversionEvents/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"deprecated": true, "description": "Deprecated: Use `GetKeyEvent` instead. Retrieve a single conversion event.", "flatPath": "v1beta/properties/{propertiesId}/conversionEvents/{conversionEventsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.conversionEvents.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the conversion event to retrieve. Format: properties/{property}/conversionEvents/{conversion_event} Example: \"properties/123/conversionEvents/456\"", "location": "path", "pattern": "^properties/[^/]+/conversionEvents/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1betaConversionEvent"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"deprecated": true, "description": "Deprecated: Use `ListKeyEvents` instead. Returns a list of conversion events in the specified parent property. Returns an empty list if no conversion events are found.", "flatPath": "v1beta/properties/{propertiesId}/conversionEvents", "httpMethod": "GET", "id": "analyticsadmin.properties.conversionEvents.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListConversionEvents` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListConversionEvents` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the parent property. Example: 'properties/123'", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/conversionEvents", "response": {"$ref": "GoogleAnalyticsAdminV1betaListConversionEventsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"deprecated": true, "description": "Deprecated: Use `UpdateKeyEvent` instead. Updates a conversion event with the specified attributes.", "flatPath": "v1beta/properties/{propertiesId}/conversionEvents/{conversionEventsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.conversionEvents.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this conversion event. Format: properties/{property}/conversionEvents/{conversion_event}", "location": "path", "pattern": "^properties/[^/]+/conversionEvents/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1betaConversionEvent"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaConversionEvent"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "customDimensions": {"methods": {"archive": {"description": "Archives a CustomDimension on a property.", "flatPath": "v1beta/properties/{propertiesId}/customDimensions/{customDimensionsId}:archive", "httpMethod": "POST", "id": "analyticsadmin.properties.customDimensions.archive", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the CustomDimension to archive. Example format: properties/1234/customDimensions/5678", "location": "path", "pattern": "^properties/[^/]+/customDimensions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}:archive", "request": {"$ref": "GoogleAnalyticsAdminV1betaArchiveCustomDimensionRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "create": {"description": "Creates a CustomDimension.", "flatPath": "v1beta/properties/{propertiesId}/customDimensions", "httpMethod": "POST", "id": "analyticsadmin.properties.customDimensions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/customDimensions", "request": {"$ref": "GoogleAnalyticsAdminV1betaCustomDimension"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaCustomDimension"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single CustomDimension.", "flatPath": "v1beta/properties/{propertiesId}/customDimensions/{customDimensionsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.customDimensions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the CustomDimension to get. Example format: properties/1234/customDimensions/5678", "location": "path", "pattern": "^properties/[^/]+/customDimensions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1betaCustomDimension"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists CustomDimensions on a property.", "flatPath": "v1beta/properties/{propertiesId}/customDimensions", "httpMethod": "GET", "id": "analyticsadmin.properties.customDimensions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListCustomDimensions` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListCustomDimensions` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/customDimensions", "response": {"$ref": "GoogleAnalyticsAdminV1betaListCustomDimensionsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a CustomDimension on a property.", "flatPath": "v1beta/properties/{propertiesId}/customDimensions/{customDimensionsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.customDimensions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name for this CustomDimension resource. Format: properties/{property}/customDimensions/{customDimension}", "location": "path", "pattern": "^properties/[^/]+/customDimensions/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1betaCustomDimension"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaCustomDimension"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "customMetrics": {"methods": {"archive": {"description": "Archives a CustomMetric on a property.", "flatPath": "v1beta/properties/{propertiesId}/customMetrics/{customMetricsId}:archive", "httpMethod": "POST", "id": "analyticsadmin.properties.customMetrics.archive", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the CustomMetric to archive. Example format: properties/1234/customMetrics/5678", "location": "path", "pattern": "^properties/[^/]+/customMetrics/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}:archive", "request": {"$ref": "GoogleAnalyticsAdminV1betaArchiveCustomMetricRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "create": {"description": "Creates a CustomMetric.", "flatPath": "v1beta/properties/{propertiesId}/customMetrics", "httpMethod": "POST", "id": "analyticsadmin.properties.customMetrics.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/customMetrics", "request": {"$ref": "GoogleAnalyticsAdminV1betaCustomMetric"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaCustomMetric"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single CustomMetric.", "flatPath": "v1beta/properties/{propertiesId}/customMetrics/{customMetricsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.customMetrics.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the CustomMetric to get. Example format: properties/1234/customMetrics/5678", "location": "path", "pattern": "^properties/[^/]+/customMetrics/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1betaCustomMetric"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists CustomMetrics on a property.", "flatPath": "v1beta/properties/{propertiesId}/customMetrics", "httpMethod": "GET", "id": "analyticsadmin.properties.customMetrics.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListCustomMetrics` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListCustomMetrics` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/customMetrics", "response": {"$ref": "GoogleAnalyticsAdminV1betaListCustomMetricsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a CustomMetric on a property.", "flatPath": "v1beta/properties/{propertiesId}/customMetrics/{customMetricsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.customMetrics.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name for this CustomMetric resource. Format: properties/{property}/customMetrics/{customMetric}", "location": "path", "pattern": "^properties/[^/]+/customMetrics/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1betaCustomMetric"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaCustomMetric"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "dataStreams": {"methods": {"create": {"description": "Creates a DataStream.", "flatPath": "v1beta/properties/{propertiesId}/dataStreams", "httpMethod": "POST", "id": "analyticsadmin.properties.dataStreams.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/dataStreams", "request": {"$ref": "GoogleAnalyticsAdminV1betaDataStream"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaDataStream"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a DataStream on a property.", "flatPath": "v1beta/properties/{propertiesId}/dataStreams/{dataStreamsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.dataStreams.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the DataStream to delete. Example format: properties/1234/dataStreams/5678", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single DataStream.", "flatPath": "v1beta/properties/{propertiesId}/dataStreams/{dataStreamsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the DataStream to get. Example format: properties/1234/dataStreams/5678", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1betaDataStream"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Lists DataStreams on a property.", "flatPath": "v1beta/properties/{propertiesId}/dataStreams", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListDataStreams` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListDataStreams` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/dataStreams", "response": {"$ref": "GoogleAnalyticsAdminV1betaListDataStreamsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a DataStream on a property.", "flatPath": "v1beta/properties/{propertiesId}/dataStreams/{dataStreamsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.dataStreams.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this Data Stream. Format: properties/{property_id}/dataStreams/{stream_id} Example: \"properties/1000/dataStreams/2000\"", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1betaDataStream"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaDataStream"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}, "resources": {"measurementProtocolSecrets": {"methods": {"create": {"description": "Creates a measurement protocol secret.", "flatPath": "v1beta/properties/{propertiesId}/dataStreams/{dataStreamsId}/measurementProtocolSecrets", "httpMethod": "POST", "id": "analyticsadmin.properties.dataStreams.measurementProtocolSecrets.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource where this secret will be created. Format: properties/{property}/dataStreams/{dataStream}", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/measurementProtocolSecrets", "request": {"$ref": "GoogleAnalyticsAdminV1betaMeasurementProtocolSecret"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaMeasurementProtocolSecret"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes target MeasurementProtocolSecret.", "flatPath": "v1beta/properties/{propertiesId}/dataStreams/{dataStreamsId}/measurementProtocolSecrets/{measurementProtocolSecretsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.dataStreams.measurementProtocolSecrets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the MeasurementProtocolSecret to delete. Format: properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/measurementProtocolSecrets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Lookup for a single MeasurementProtocolSecret.", "flatPath": "v1beta/properties/{propertiesId}/dataStreams/{dataStreamsId}/measurementProtocolSecrets/{measurementProtocolSecretsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.measurementProtocolSecrets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the measurement protocol secret to lookup. Format: properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/measurementProtocolSecrets/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1betaMeasurementProtocolSecret"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Returns child MeasurementProtocolSecrets under the specified parent Property.", "flatPath": "v1beta/properties/{propertiesId}/dataStreams/{dataStreamsId}/measurementProtocolSecrets", "httpMethod": "GET", "id": "analyticsadmin.properties.dataStreams.measurementProtocolSecrets.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 10 resources will be returned. The maximum value is 10. Higher values will be coerced to the maximum.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListMeasurementProtocolSecrets` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListMeasurementProtocolSecrets` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the parent stream. Format: properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/measurementProtocolSecrets", "response": {"$ref": "GoogleAnalyticsAdminV1betaListMeasurementProtocolSecretsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a measurement protocol secret.", "flatPath": "v1beta/properties/{propertiesId}/dataStreams/{dataStreamsId}/measurementProtocolSecrets/{measurementProtocolSecretsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.dataStreams.measurementProtocolSecrets.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this secret. This secret may be a child of any type of stream. Format: properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}", "location": "path", "pattern": "^properties/[^/]+/dataStreams/[^/]+/measurementProtocolSecrets/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Omitted fields will not be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1betaMeasurementProtocolSecret"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaMeasurementProtocolSecret"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}}}, "firebaseLinks": {"methods": {"create": {"description": "Creates a FirebaseLink. Properties can have at most one FirebaseLink.", "flatPath": "v1beta/properties/{propertiesId}/firebaseLinks", "httpMethod": "POST", "id": "analyticsadmin.properties.firebaseLinks.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Format: properties/{property_id} Example: `properties/1234`", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/firebaseLinks", "request": {"$ref": "GoogleAnalyticsAdminV1betaFirebaseLink"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaFirebaseLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a FirebaseLink on a property", "flatPath": "v1beta/properties/{propertiesId}/firebaseLinks/{firebaseLinksId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.firebaseLinks.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Format: properties/{property_id}/firebaseLinks/{firebase_link_id} Example: `properties/1234/firebaseLinks/5678`", "location": "path", "pattern": "^properties/[^/]+/firebaseLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "list": {"description": "Lists FirebaseLinks on a property. Properties can have at most one FirebaseLink.", "flatPath": "v1beta/properties/{propertiesId}/firebaseLinks", "httpMethod": "GET", "id": "analyticsadmin.properties.firebaseLinks.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. The service may return fewer than this value, even if there are additional pages. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListFirebaseLinks` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListFirebaseLinks` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Format: properties/{property_id} Example: `properties/1234`", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/firebaseLinks", "response": {"$ref": "GoogleAnalyticsAdminV1betaListFirebaseLinksResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}}}, "googleAdsLinks": {"methods": {"create": {"description": "Creates a GoogleAdsLink.", "flatPath": "v1beta/properties/{propertiesId}/googleAdsLinks", "httpMethod": "POST", "id": "analyticsadmin.properties.googleAdsLinks.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/googleAdsLinks", "request": {"$ref": "GoogleAnalyticsAdminV1betaGoogleAdsLink"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaGoogleAdsLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a GoogleAdsLink on a property", "flatPath": "v1beta/properties/{propertiesId}/googleAdsLinks/{googleAdsLinksId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.googleAdsLinks.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Example format: properties/1234/googleAdsLinks/5678", "location": "path", "pattern": "^properties/[^/]+/googleAdsLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "list": {"description": "Lists GoogleAdsLinks on a property.", "flatPath": "v1beta/properties/{propertiesId}/googleAdsLinks", "httpMethod": "GET", "id": "analyticsadmin.properties.googleAdsLinks.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200 (higher values will be coerced to the maximum).", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListGoogleAdsLinks` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListGoogleAdsLinks` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Example format: properties/1234", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/googleAdsLinks", "response": {"$ref": "GoogleAnalyticsAdminV1betaListGoogleAdsLinksResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a GoogleAdsLink on a property", "flatPath": "v1beta/properties/{propertiesId}/googleAdsLinks/{googleAdsLinksId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.googleAdsLinks.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Format: properties/{propertyId}/googleAdsLinks/{googleAdsLinkId} Note: googleAdsLinkId is not the Google Ads customer ID.", "location": "path", "pattern": "^properties/[^/]+/googleAdsLinks/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1betaGoogleAdsLink"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaGoogleAdsLink"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}, "keyEvents": {"methods": {"create": {"description": "Creates a Key Event.", "flatPath": "v1beta/properties/{propertiesId}/keyEvents", "httpMethod": "POST", "id": "analyticsadmin.properties.keyEvents.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the parent property where this Key Event will be created. Format: properties/123", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/keyEvents", "request": {"$ref": "GoogleAnalyticsAdminV1betaKeyEvent"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaKeyEvent"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "delete": {"description": "Deletes a Key Event.", "flatPath": "v1beta/properties/{propertiesId}/keyEvents/{keyEventsId}", "httpMethod": "DELETE", "id": "analyticsadmin.properties.keyEvents.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Key Event to delete. Format: properties/{property}/keyEvents/{key_event} Example: \"properties/123/keyEvents/456\"", "location": "path", "pattern": "^properties/[^/]+/keyEvents/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}, "get": {"description": "Retrieve a single Key Event.", "flatPath": "v1beta/properties/{propertiesId}/keyEvents/{keyEventsId}", "httpMethod": "GET", "id": "analyticsadmin.properties.keyEvents.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Key Event to retrieve. Format: properties/{property}/keyEvents/{key_event} Example: \"properties/123/keyEvents/456\"", "location": "path", "pattern": "^properties/[^/]+/keyEvents/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+name}", "response": {"$ref": "GoogleAnalyticsAdminV1betaKeyEvent"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "list": {"description": "Returns a list of Key Events in the specified parent property. Returns an empty list if no Key Events are found.", "flatPath": "v1beta/properties/{propertiesId}/keyEvents", "httpMethod": "GET", "id": "analyticsadmin.properties.keyEvents.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of resources to return. If unspecified, at most 50 resources will be returned. The maximum value is 200; (higher values will be coerced to the maximum)", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListKeyEvents` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListKeyEvents` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the parent property. Example: 'properties/123'", "location": "path", "pattern": "^properties/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta/{+parent}/keyEvents", "response": {"$ref": "GoogleAnalyticsAdminV1betaListKeyEventsResponse"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit", "https://www.googleapis.com/auth/analytics.readonly"]}, "patch": {"description": "Updates a Key Event.", "flatPath": "v1beta/properties/{propertiesId}/keyEvents/{keyEventsId}", "httpMethod": "PATCH", "id": "analyticsadmin.properties.keyEvents.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of this key event. Format: properties/{property}/keyEvents/{key_event}", "location": "path", "pattern": "^properties/[^/]+/keyEvents/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. Field names must be in snake case (e.g., \"field_to_update\"). Omitted fields will not be updated. To replace the entire entity, use one path with the string \"*\" to match all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta/{+name}", "request": {"$ref": "GoogleAnalyticsAdminV1betaKeyEvent"}, "response": {"$ref": "GoogleAnalyticsAdminV1betaKeyEvent"}, "scopes": ["https://www.googleapis.com/auth/analytics.edit"]}}}}}}, "revision": "20250307", "rootUrl": "https://analyticsadmin.googleapis.com/", "schemas": {"GoogleAnalyticsAdminV1betaAccessBetweenFilter": {"description": "To express that the result needs to be between two numbers (inclusive).", "id": "GoogleAnalyticsAdminV1betaAccessBetweenFilter", "properties": {"fromValue": {"$ref": "GoogleAnalyticsAdminV1betaNumericValue", "description": "Begins with this number."}, "toValue": {"$ref": "GoogleAnalyticsAdminV1betaNumericValue", "description": "Ends with this number."}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessDateRange": {"description": "A contiguous range of days: startDate, startDate + 1, ..., endDate.", "id": "GoogleAnalyticsAdminV1betaAccessDateRange", "properties": {"endDate": {"description": "The inclusive end date for the query in the format `YYYY-MM-DD`. Cannot be before `startDate`. The format `NdaysAgo`, `yesterday`, or `today` is also accepted, and in that case, the date is inferred based on the current time in the request's time zone.", "type": "string"}, "startDate": {"description": "The inclusive start date for the query in the format `YYYY-MM-DD`. Cannot be after `endDate`. The format `NdaysAgo`, `yesterday`, or `today` is also accepted, and in that case, the date is inferred based on the current time in the request's time zone.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessDimension": {"description": "Dimensions are attributes of your data. For example, the dimension `userEmail` indicates the email of the user that accessed reporting data. Dimension values in report responses are strings.", "id": "GoogleAnalyticsAdminV1betaAccessDimension", "properties": {"dimensionName": {"description": "The API name of the dimension. See [Data Access Schema](https://developers.google.com/analytics/devguides/config/admin/v1/access-api-schema) for the list of dimensions supported in this API. Dimensions are referenced by name in `dimensionFilter` and `orderBys`.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessDimensionHeader": {"description": "Describes a dimension column in the report. Dimensions requested in a report produce column entries within rows and DimensionHeaders. However, dimensions used exclusively within filters or expressions do not produce columns in a report; correspondingly, those dimensions do not produce headers.", "id": "GoogleAnalyticsAdminV1betaAccessDimensionHeader", "properties": {"dimensionName": {"description": "The dimension's name; for example 'userEmail'.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessDimensionValue": {"description": "The value of a dimension.", "id": "GoogleAnalyticsAdminV1betaAccessDimensionValue", "properties": {"value": {"description": "The dimension value. For example, this value may be 'France' for the 'country' dimension.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessFilter": {"description": "An expression to filter dimension or metric values.", "id": "GoogleAnalyticsAdminV1betaAccessFilter", "properties": {"betweenFilter": {"$ref": "GoogleAnalyticsAdminV1betaAccessBetweenFilter", "description": "A filter for two values."}, "fieldName": {"description": "The dimension name or metric name.", "type": "string"}, "inListFilter": {"$ref": "GoogleAnalyticsAdminV1betaAccessInListFilter", "description": "A filter for in list values."}, "numericFilter": {"$ref": "GoogleAnalyticsAdminV1betaAccessNumericFilter", "description": "A filter for numeric or date values."}, "stringFilter": {"$ref": "GoogleAnalyticsAdminV1betaAccessStringFilter", "description": "Strings related filter."}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessFilterExpression": {"description": "Expresses dimension or metric filters. The fields in the same expression need to be either all dimensions or all metrics.", "id": "GoogleAnalyticsAdminV1betaAccessFilterExpression", "properties": {"accessFilter": {"$ref": "GoogleAnalyticsAdminV1betaAccessFilter", "description": "A primitive filter. In the same FilterExpression, all of the filter's field names need to be either all dimensions or all metrics."}, "andGroup": {"$ref": "GoogleAnalyticsAdminV1betaAccessFilterExpressionList", "description": "Each of the FilterExpressions in the and_group has an AND relationship."}, "notExpression": {"$ref": "GoogleAnalyticsAdminV1betaAccessFilterExpression", "description": "The FilterExpression is NOT of not_expression."}, "orGroup": {"$ref": "GoogleAnalyticsAdminV1betaAccessFilterExpressionList", "description": "Each of the FilterExpressions in the or_group has an OR relationship."}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessFilterExpressionList": {"description": "A list of filter expressions.", "id": "GoogleAnalyticsAdminV1betaAccessFilterExpressionList", "properties": {"expressions": {"description": "A list of filter expressions.", "items": {"$ref": "GoogleAnalyticsAdminV1betaAccessFilterExpression"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessInListFilter": {"description": "The result needs to be in a list of string values.", "id": "GoogleAnalyticsAdminV1betaAccessInListFilter", "properties": {"caseSensitive": {"description": "If true, the string value is case sensitive.", "type": "boolean"}, "values": {"description": "The list of string values. Must be non-empty.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessMetric": {"description": "The quantitative measurements of a report. For example, the metric `accessCount` is the total number of data access records.", "id": "GoogleAnalyticsAdminV1betaAccessMetric", "properties": {"metricName": {"description": "The API name of the metric. See [Data Access Schema](https://developers.google.com/analytics/devguides/config/admin/v1/access-api-schema) for the list of metrics supported in this API. Metrics are referenced by name in `metricFilter` & `orderBys`.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessMetricHeader": {"description": "Describes a metric column in the report. Visible metrics requested in a report produce column entries within rows and MetricHeaders. However, metrics used exclusively within filters or expressions do not produce columns in a report; correspondingly, those metrics do not produce headers.", "id": "GoogleAnalyticsAdminV1betaAccessMetricHeader", "properties": {"metricName": {"description": "The metric's name; for example 'accessCount'.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessMetricValue": {"description": "The value of a metric.", "id": "GoogleAnalyticsAdminV1betaAccessMetricValue", "properties": {"value": {"description": "The measurement value. For example, this value may be '13'.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessNumericFilter": {"description": "Filters for numeric or date values.", "id": "GoogleAnalyticsAdminV1betaAccessNumericFilter", "properties": {"operation": {"description": "The operation type for this filter.", "enum": ["OPERATION_UNSPECIFIED", "EQUAL", "LESS_THAN", "LESS_THAN_OR_EQUAL", "GREATER_THAN", "GREATER_THAN_OR_EQUAL"], "enumDescriptions": ["Unspecified.", "Equal", "Less than", "Less than or equal", "Greater than", "Greater than or equal"], "type": "string"}, "value": {"$ref": "GoogleAnalyticsAdminV1betaNumericValue", "description": "A numeric value or a date value."}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessOrderBy": {"description": "Order bys define how rows will be sorted in the response. For example, ordering rows by descending access count is one ordering, and ordering rows by the country string is a different ordering.", "id": "GoogleAnalyticsAdminV1betaAccessOrderBy", "properties": {"desc": {"description": "If true, sorts by descending order. If false or unspecified, sorts in ascending order.", "type": "boolean"}, "dimension": {"$ref": "GoogleAnalyticsAdminV1betaAccessOrderByDimensionOrderBy", "description": "Sorts results by a dimension's values."}, "metric": {"$ref": "GoogleAnalyticsAdminV1betaAccessOrderByMetricOrderBy", "description": "Sorts results by a metric's values."}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessOrderByDimensionOrderBy": {"description": "Sorts by dimension values.", "id": "GoogleAnalyticsAdminV1betaAccessOrderByDimensionOrderBy", "properties": {"dimensionName": {"description": "A dimension name in the request to order by.", "type": "string"}, "orderType": {"description": "Controls the rule for dimension value ordering.", "enum": ["ORDER_TYPE_UNSPECIFIED", "ALPHANUMERIC", "CASE_INSENSITIVE_ALPHANUMERIC", "NUMERIC"], "enumDescriptions": ["Unspecified.", "Alphanumeric sort by Unicode code point. For example, \"2\" < \"A\" < \"X\" < \"b\" < \"z\".", "Case insensitive alphanumeric sort by lower case Unicode code point. For example, \"2\" < \"A\" < \"b\" < \"X\" < \"z\".", "Dimension values are converted to numbers before sorting. For example in NUMERIC sort, \"25\" < \"100\", and in `ALPHANUMERIC` sort, \"100\" < \"25\". Non-numeric dimension values all have equal ordering value below all numeric values."], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessOrderByMetricOrderBy": {"description": "Sorts by metric values.", "id": "GoogleAnalyticsAdminV1betaAccessOrderByMetricOrderBy", "properties": {"metricName": {"description": "A metric name in the request to order by.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessQuota": {"description": "Current state of all quotas for this Analytics property. If any quota for a property is exhausted, all requests to that property will return Resource Exhausted errors.", "id": "GoogleAnalyticsAdminV1betaAccessQuota", "properties": {"concurrentRequests": {"$ref": "GoogleAnalyticsAdminV1betaAccessQuotaStatus", "description": "Properties can use up to 50 concurrent requests."}, "serverErrorsPerProjectPerHour": {"$ref": "GoogleAnalyticsAdminV1betaAccessQuotaStatus", "description": "Properties and cloud project pairs can have up to 50 server errors per hour."}, "tokensPerDay": {"$ref": "GoogleAnalyticsAdminV1betaAccessQuotaStatus", "description": "Properties can use 250,000 tokens per day. Most requests consume fewer than 10 tokens."}, "tokensPerHour": {"$ref": "GoogleAnalyticsAdminV1betaAccessQuotaStatus", "description": "Properties can use 50,000 tokens per hour. An API request consumes a single number of tokens, and that number is deducted from all of the hourly, daily, and per project hourly quotas."}, "tokensPerProjectPerHour": {"$ref": "GoogleAnalyticsAdminV1betaAccessQuotaStatus", "description": "Properties can use up to 25% of their tokens per project per hour. This amounts to Analytics 360 Properties can use 12,500 tokens per project per hour. An API request consumes a single number of tokens, and that number is deducted from all of the hourly, daily, and per project hourly quotas."}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessQuotaStatus": {"description": "Current state for a particular quota group.", "id": "GoogleAnalyticsAdminV1betaAccessQuotaStatus", "properties": {"consumed": {"description": "<PERSON><PERSON><PERSON> consumed by this request.", "format": "int32", "type": "integer"}, "remaining": {"description": "<PERSON><PERSON><PERSON> remaining after this request.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessRow": {"description": "Access report data for each row.", "id": "GoogleAnalyticsAdminV1betaAccessRow", "properties": {"dimensionValues": {"description": "List of dimension values. These values are in the same order as specified in the request.", "items": {"$ref": "GoogleAnalyticsAdminV1betaAccessDimensionValue"}, "type": "array"}, "metricValues": {"description": "List of metric values. These values are in the same order as specified in the request.", "items": {"$ref": "GoogleAnalyticsAdminV1betaAccessMetricValue"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccessStringFilter": {"description": "The filter for strings.", "id": "GoogleAnalyticsAdminV1betaAccessStringFilter", "properties": {"caseSensitive": {"description": "If true, the string value is case sensitive.", "type": "boolean"}, "matchType": {"description": "The match type for this filter.", "enum": ["MATCH_TYPE_UNSPECIFIED", "EXACT", "BEGINS_WITH", "ENDS_WITH", "CONTAINS", "FULL_REGEXP", "PARTIAL_REGEXP"], "enumDescriptions": ["Unspecified", "Exact match of the string value.", "Begins with the string value.", "Ends with the string value.", "Contains the string value.", "Full match for the regular expression with the string value.", "Partial match for the regular expression with the string value."], "type": "string"}, "value": {"description": "The string value used for the matching.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccount": {"description": "A resource message representing a Google Analytics account.", "id": "GoogleAnalyticsAdminV1betaAccount", "properties": {"createTime": {"description": "Output only. Time when this account was originally created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleted": {"description": "Output only. Indicates whether this Account is soft-deleted or not. Deleted accounts are excluded from List results unless specifically requested.", "readOnly": true, "type": "boolean"}, "displayName": {"description": "Required. Human-readable display name for this account.", "type": "string"}, "gmpOrganization": {"description": "Output only. The URI for a Google Marketing Platform organization resource. Only set when this account is connected to a GMP organization. Format: marketingplatformadmin.googleapis.com/organizations/{org_id}", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Resource name of this account. Format: accounts/{account} Example: \"accounts/100\"", "readOnly": true, "type": "string"}, "regionCode": {"description": "Country of business. Must be a Unicode CLDR region code.", "type": "string"}, "updateTime": {"description": "Output only. Time when account payload fields were last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAccountSummary": {"description": "A virtual resource representing an overview of an account and all its child Google Analytics properties.", "id": "GoogleAnalyticsAdminV1betaAccountSummary", "properties": {"account": {"description": "Resource name of account referred to by this account summary Format: accounts/{account_id} Example: \"accounts/1000\"", "type": "string"}, "displayName": {"description": "Display name for the account referred to in this account summary.", "type": "string"}, "name": {"description": "Resource name for this account summary. Format: accountSummaries/{account_id} Example: \"accountSummaries/1000\"", "type": "string"}, "propertySummaries": {"description": "List of summaries for child accounts of this account.", "items": {"$ref": "GoogleAnalyticsAdminV1betaPropertySummary"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAcknowledgeUserDataCollectionRequest": {"description": "Request message for AcknowledgeUserDataCollection RPC.", "id": "GoogleAnalyticsAdminV1betaAcknowledgeUserDataCollectionRequest", "properties": {"acknowledgement": {"description": "Required. An acknowledgement that the caller of this method understands the terms of user data collection. This field must contain the exact value: \"I acknowledge that I have the necessary privacy disclosures and rights from my end users for the collection and processing of their data, including the association of such data with the visitation information Google Analytics collects from my site and/or app property.\"", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaAcknowledgeUserDataCollectionResponse": {"description": "Response message for AcknowledgeUserDataCollection RPC.", "id": "GoogleAnalyticsAdminV1betaAcknowledgeUserDataCollectionResponse", "properties": {}, "type": "object"}, "GoogleAnalyticsAdminV1betaArchiveCustomDimensionRequest": {"description": "Request message for ArchiveCustomDimension RPC.", "id": "GoogleAnalyticsAdminV1betaArchiveCustomDimensionRequest", "properties": {}, "type": "object"}, "GoogleAnalyticsAdminV1betaArchiveCustomMetricRequest": {"description": "Request message for ArchiveCustomMetric RPC.", "id": "GoogleAnalyticsAdminV1betaArchiveCustomMetricRequest", "properties": {}, "type": "object"}, "GoogleAnalyticsAdminV1betaChangeHistoryChange": {"description": "A description of a change to a single Google Analytics resource.", "id": "GoogleAnalyticsAdminV1betaChangeHistoryChange", "properties": {"action": {"description": "The type of action that changed this resource.", "enum": ["ACTION_TYPE_UNSPECIFIED", "CREATED", "UPDATED", "DELETED"], "enumDescriptions": ["Action type unknown or not specified.", "Resource was created in this change.", "Resource was updated in this change.", "Resource was deleted in this change."], "type": "string"}, "resource": {"description": "Resource name of the resource whose changes are described by this entry.", "type": "string"}, "resourceAfterChange": {"$ref": "GoogleAnalyticsAdminV1betaChangeHistoryChangeChangeHistoryResource", "description": "Resource contents from after the change was made. If this resource was deleted in this change, this field will be missing."}, "resourceBeforeChange": {"$ref": "GoogleAnalyticsAdminV1betaChangeHistoryChangeChangeHistoryResource", "description": "Resource contents from before the change was made. If this resource was created in this change, this field will be missing."}}, "type": "object"}, "GoogleAnalyticsAdminV1betaChangeHistoryChangeChangeHistoryResource": {"description": "A snapshot of a resource as before or after the result of a change in change history.", "id": "GoogleAnalyticsAdminV1betaChangeHistoryChangeChangeHistoryResource", "properties": {"account": {"$ref": "GoogleAnalyticsAdminV1betaAccount", "description": "A snapshot of an Account resource in change history."}, "conversionEvent": {"$ref": "GoogleAnalyticsAdminV1betaConversionEvent", "description": "A snapshot of a ConversionEvent resource in change history."}, "dataRetentionSettings": {"$ref": "GoogleAnalyticsAdminV1betaDataRetentionSettings", "description": "A snapshot of a data retention settings resource in change history."}, "dataStream": {"$ref": "GoogleAnalyticsAdminV1betaDataStream", "description": "A snapshot of a DataStream resource in change history."}, "firebaseLink": {"$ref": "GoogleAnalyticsAdminV1betaFirebaseLink", "description": "A snapshot of a FirebaseLink resource in change history."}, "googleAdsLink": {"$ref": "GoogleAnalyticsAdminV1betaGoogleAdsLink", "description": "A snapshot of a GoogleAdsLink resource in change history."}, "measurementProtocolSecret": {"$ref": "GoogleAnalyticsAdminV1betaMeasurementProtocolSecret", "description": "A snapshot of a MeasurementProtocolSecret resource in change history."}, "property": {"$ref": "GoogleAnalyticsAdminV1betaProperty", "description": "A snapshot of a Property resource in change history."}}, "type": "object"}, "GoogleAnalyticsAdminV1betaChangeHistoryEvent": {"description": "A set of changes within a Google Analytics account or its child properties that resulted from the same cause. Common causes would be updates made in the Google Analytics UI, changes from customer support, or automatic Google Analytics system changes.", "id": "GoogleAnalyticsAdminV1betaChangeHistoryEvent", "properties": {"actorType": {"description": "The type of actor that made this change.", "enum": ["ACTOR_TYPE_UNSPECIFIED", "USER", "SYSTEM", "SUPPORT"], "enumDescriptions": ["Unknown or unspecified actor type.", "Changes made by the user specified in actor_email.", "Changes made by the Google Analytics system.", "Changes made by Google Analytics support team staff."], "type": "string"}, "changeTime": {"description": "Time when change was made.", "format": "google-datetime", "type": "string"}, "changes": {"description": "A list of changes made in this change history event that fit the filters specified in SearchChangeHistoryEventsRequest.", "items": {"$ref": "GoogleAnalyticsAdminV1betaChangeHistoryChange"}, "type": "array"}, "changesFiltered": {"description": "If true, then the list of changes returned was filtered, and does not represent all changes that occurred in this event.", "type": "boolean"}, "id": {"description": "ID of this change history event. This ID is unique across Google Analytics.", "type": "string"}, "userActorEmail": {"description": "Email address of the Google account that made the change. This will be a valid email address if the actor field is set to USER, and empty otherwise. Google accounts that have been deleted will cause an error.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaConversionEvent": {"description": "A conversion event in a Google Analytics property.", "id": "GoogleAnalyticsAdminV1betaConversionEvent", "properties": {"countingMethod": {"description": "Optional. The method by which conversions will be counted across multiple events within a session. If this value is not provided, it will be set to `ONCE_PER_EVENT`.", "enum": ["CONVERSION_COUNTING_METHOD_UNSPECIFIED", "ONCE_PER_EVENT", "ONCE_PER_SESSION"], "enumDescriptions": ["Counting method not specified.", "Each Event instance is considered a Conversion.", "An Event instance is considered a Conversion at most once per session per user."], "type": "string"}, "createTime": {"description": "Output only. Time when this conversion event was created in the property.", "format": "google-datetime", "readOnly": true, "type": "string"}, "custom": {"description": "Output only. If set to true, this conversion event refers to a custom event. If set to false, this conversion event refers to a default event in GA. Default events typically have special meaning in GA. Default events are usually created for you by the GA system, but in some cases can be created by property admins. Custom events count towards the maximum number of custom conversion events that may be created per property.", "readOnly": true, "type": "boolean"}, "defaultConversionValue": {"$ref": "GoogleAnalyticsAdminV1betaConversionEventDefaultConversionValue", "description": "Optional. Defines a default value/currency for a conversion event."}, "deletable": {"description": "Output only. If set, this event can currently be deleted with DeleteConversionEvent.", "readOnly": true, "type": "boolean"}, "eventName": {"description": "Immutable. The event name for this conversion event. Examples: 'click', 'purchase'", "type": "string"}, "name": {"description": "Output only. Resource name of this conversion event. Format: properties/{property}/conversionEvents/{conversion_event}", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaConversionEventDefaultConversionValue": {"description": "Defines a default value/currency for a conversion event. Both value and currency must be provided.", "id": "GoogleAnalyticsAdminV1betaConversionEventDefaultConversionValue", "properties": {"currencyCode": {"description": "When a conversion event for this event_name has no set currency, this currency will be applied as the default. Must be in ISO 4217 currency code format. See https://en.wikipedia.org/wiki/ISO_4217 for more information.", "type": "string"}, "value": {"description": "This value will be used to populate the value for all conversions of the specified event_name where the event \"value\" parameter is unset.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaCustomDimension": {"description": "A definition for a CustomDimension.", "id": "GoogleAnalyticsAdminV1betaCustomDimension", "properties": {"description": {"description": "Optional. Description for this custom dimension. Max length of 150 characters.", "type": "string"}, "disallowAdsPersonalization": {"description": "Optional. If set to true, sets this dimension as NPA and excludes it from ads personalization. This is currently only supported by user-scoped custom dimensions.", "type": "boolean"}, "displayName": {"description": "Required. Display name for this custom dimension as shown in the Analytics UI. Max length of 82 characters, alphanumeric plus space and underscore starting with a letter. Legacy system-generated display names may contain square brackets, but updates to this field will never permit square brackets.", "type": "string"}, "name": {"description": "Output only. Resource name for this CustomDimension resource. Format: properties/{property}/customDimensions/{customDimension}", "readOnly": true, "type": "string"}, "parameterName": {"description": "Required. Immutable. Tagging parameter name for this custom dimension. If this is a user-scoped dimension, then this is the user property name. If this is an event-scoped dimension, then this is the event parameter name. If this is an item-scoped dimension, then this is the parameter name found in the eCommerce items array. May only contain alphanumeric and underscore characters, starting with a letter. Max length of 24 characters for user-scoped dimensions, 40 characters for event-scoped dimensions.", "type": "string"}, "scope": {"description": "Required. Immutable. The scope of this dimension.", "enum": ["DIMENSION_SCOPE_UNSPECIFIED", "EVENT", "USER", "ITEM"], "enumDescriptions": ["Scope unknown or not specified.", "Dimension scoped to an event.", "Dimension scoped to a user.", "Dimension scoped to eCommerce items"], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaCustomMetric": {"description": "A definition for a custom metric.", "id": "GoogleAnalyticsAdminV1betaCustomMetric", "properties": {"description": {"description": "Optional. Description for this custom dimension. Max length of 150 characters.", "type": "string"}, "displayName": {"description": "Required. Display name for this custom metric as shown in the Analytics UI. Max length of 82 characters, alphanumeric plus space and underscore starting with a letter. Legacy system-generated display names may contain square brackets, but updates to this field will never permit square brackets.", "type": "string"}, "measurementUnit": {"description": "Required. The type for the custom metric's value.", "enum": ["MEASUREMENT_UNIT_UNSPECIFIED", "STANDARD", "CURRENCY", "FEET", "METERS", "KILOMETERS", "MILES", "MILLISECONDS", "SECONDS", "MINUTES", "HOURS"], "enumDescriptions": ["MeasurementUnit unspecified or missing.", "This metric uses default units.", "This metric measures a currency.", "This metric measures feet.", "This metric measures meters.", "This metric measures kilometers.", "This metric measures miles.", "This metric measures milliseconds.", "This metric measures seconds.", "This metric measures minutes.", "This metric measures hours."], "type": "string"}, "name": {"description": "Output only. Resource name for this CustomMetric resource. Format: properties/{property}/customMetrics/{customMetric}", "readOnly": true, "type": "string"}, "parameterName": {"description": "Required. Immutable. Tagging name for this custom metric. If this is an event-scoped metric, then this is the event parameter name. May only contain alphanumeric and underscore charactes, starting with a letter. Max length of 40 characters for event-scoped metrics.", "type": "string"}, "restrictedMetricType": {"description": "Optional. Types of restricted data that this metric may contain. Required for metrics with CURRENCY measurement unit. Must be empty for metrics with a non-CURRENCY measurement unit.", "items": {"enum": ["RESTRICTED_METRIC_TYPE_UNSPECIFIED", "COST_DATA", "REVENUE_DATA"], "enumDescriptions": ["Type unknown or unspecified.", "Metric reports cost data.", "Metric reports revenue data."], "type": "string"}, "type": "array"}, "scope": {"description": "Required. Immutable. The scope of this custom metric.", "enum": ["METRIC_SCOPE_UNSPECIFIED", "EVENT"], "enumDescriptions": ["Scope unknown or not specified.", "Metric scoped to an event."], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaDataRetentionSettings": {"description": "Settings values for data retention. This is a singleton resource.", "id": "GoogleAnalyticsAdminV1betaDataRetentionSettings", "properties": {"eventDataRetention": {"description": "Required. The length of time that event-level data is retained.", "enum": ["RETENTION_DURATION_UNSPECIFIED", "TWO_MONTHS", "FOURTEEN_MONTHS", "TWENTY_SIX_MONTHS", "THIRTY_EIGHT_MONTHS", "FIFTY_MONTHS"], "enumDescriptions": ["Data retention time duration is not specified.", "The data retention time duration is 2 months.", "The data retention time duration is 14 months.", "The data retention time duration is 26 months. Available to 360 properties only. Available for event data only.", "The data retention time duration is 38 months. Available to 360 properties only. Available for event data only.", "The data retention time duration is 50 months. Available to 360 properties only. Available for event data only."], "type": "string"}, "name": {"description": "Output only. Resource name for this DataRetentionSetting resource. Format: properties/{property}/dataRetentionSettings", "readOnly": true, "type": "string"}, "resetUserDataOnNewActivity": {"description": "If true, reset the retention period for the user identifier with every event from that user.", "type": "boolean"}, "userDataRetention": {"description": "Required. The length of time that user-level data is retained.", "enum": ["RETENTION_DURATION_UNSPECIFIED", "TWO_MONTHS", "FOURTEEN_MONTHS", "TWENTY_SIX_MONTHS", "THIRTY_EIGHT_MONTHS", "FIFTY_MONTHS"], "enumDescriptions": ["Data retention time duration is not specified.", "The data retention time duration is 2 months.", "The data retention time duration is 14 months.", "The data retention time duration is 26 months. Available to 360 properties only. Available for event data only.", "The data retention time duration is 38 months. Available to 360 properties only. Available for event data only.", "The data retention time duration is 50 months. Available to 360 properties only. Available for event data only."], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaDataSharingSettings": {"description": "A resource message representing data sharing settings of a Google Analytics account.", "id": "GoogleAnalyticsAdminV1betaDataSharingSettings", "properties": {"name": {"description": "Output only. Resource name. Format: accounts/{account}/dataSharingSettings Example: \"accounts/1000/dataSharingSettings\"", "readOnly": true, "type": "string"}, "sharingWithGoogleAnySalesEnabled": {"deprecated": true, "description": "Deprecated. This field is no longer used and always returns false.", "type": "boolean"}, "sharingWithGoogleAssignedSalesEnabled": {"description": "Allows Google access to your Google Analytics account data, including account usage and configuration data, product spending, and users associated with your Google Analytics account, so that Google can help you make the most of Google products, providing you with insights, offers, recommendations, and optimization tips across Google Analytics and other Google products for business. This field maps to the \"Recommendations for your business\" field in the Google Analytics Admin UI.", "type": "boolean"}, "sharingWithGoogleProductsEnabled": {"description": "Allows Google to use the data to improve other Google products or services. This fields maps to the \"Google products & services\" field in the Google Analytics Admin UI.", "type": "boolean"}, "sharingWithGoogleSupportEnabled": {"description": "Allows Google technical support representatives access to your Google Analytics data and account when necessary to provide service and find solutions to technical issues. This field maps to the \"Technical support\" field in the Google Analytics Admin UI.", "type": "boolean"}, "sharingWithOthersEnabled": {"description": "Enable features like predictions, modeled data, and benchmarking that can provide you with richer business insights when you contribute aggregated measurement data. The data you share (including information about the property from which it is shared) is aggregated and de-identified before being used to generate business insights. This field maps to the \"Modeling contributions & business insights\" field in the Google Analytics Admin UI.", "type": "boolean"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaDataStream": {"description": "A resource message representing a data stream.", "id": "GoogleAnalyticsAdminV1betaDataStream", "properties": {"androidAppStreamData": {"$ref": "GoogleAnalyticsAdminV1betaDataStreamAndroidAppStreamData", "description": "Data specific to Android app streams. Must be populated if type is ANDROID_APP_DATA_STREAM."}, "createTime": {"description": "Output only. Time when this stream was originally created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Human-readable display name for the Data Stream. Required for web data streams. The max allowed display name length is 255 UTF-16 code units.", "type": "string"}, "iosAppStreamData": {"$ref": "GoogleAnalyticsAdminV1betaDataStreamIosAppStreamData", "description": "Data specific to iOS app streams. Must be populated if type is IOS_APP_DATA_STREAM."}, "name": {"description": "Output only. Resource name of this Data Stream. Format: properties/{property_id}/dataStreams/{stream_id} Example: \"properties/1000/dataStreams/2000\"", "readOnly": true, "type": "string"}, "type": {"description": "Required. Immutable. The type of this DataStream resource.", "enum": ["DATA_STREAM_TYPE_UNSPECIFIED", "WEB_DATA_STREAM", "ANDROID_APP_DATA_STREAM", "IOS_APP_DATA_STREAM"], "enumDescriptions": ["Type unknown or not specified.", "Web data stream.", "Android app data stream.", "iOS app data stream."], "type": "string"}, "updateTime": {"description": "Output only. Time when stream payload fields were last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "webStreamData": {"$ref": "GoogleAnalyticsAdminV1betaDataStreamWebStreamData", "description": "Data specific to web streams. Must be populated if type is WEB_DATA_STREAM."}}, "type": "object"}, "GoogleAnalyticsAdminV1betaDataStreamAndroidAppStreamData": {"description": "Data specific to Android app streams.", "id": "GoogleAnalyticsAdminV1betaDataStreamAndroidAppStreamData", "properties": {"firebaseAppId": {"description": "Output only. ID of the corresponding Android app in Firebase, if any. This ID can change if the Android app is deleted and recreated.", "readOnly": true, "type": "string"}, "packageName": {"description": "Immutable. The package name for the app being measured. Example: \"com.example.myandroidapp\"", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaDataStreamIosAppStreamData": {"description": "Data specific to iOS app streams.", "id": "GoogleAnalyticsAdminV1betaDataStreamIosAppStreamData", "properties": {"bundleId": {"description": "Required. Immutable. The Apple App Store Bundle ID for the app Example: \"com.example.myiosapp\"", "type": "string"}, "firebaseAppId": {"description": "Output only. ID of the corresponding iOS app in Firebase, if any. This ID can change if the iOS app is deleted and recreated.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaDataStreamWebStreamData": {"description": "Data specific to web streams.", "id": "GoogleAnalyticsAdminV1betaDataStreamWebStreamData", "properties": {"defaultUri": {"description": "Domain name of the web app being measured, or empty. Example: \"http://www.google.com\", \"https://www.google.com\"", "type": "string"}, "firebaseAppId": {"description": "Output only. ID of the corresponding web app in Firebase, if any. This ID can change if the web app is deleted and recreated.", "readOnly": true, "type": "string"}, "measurementId": {"description": "Output only. Analytics Measurement ID. Example: \"G-1A2BCD345E\"", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaFirebaseLink": {"description": "A link between a Google Analytics property and a Firebase project.", "id": "GoogleAnalyticsAdminV1betaFirebaseLink", "properties": {"createTime": {"description": "Output only. Time when this FirebaseLink was originally created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Example format: properties/1234/firebaseLinks/5678", "readOnly": true, "type": "string"}, "project": {"description": "Immutable. Firebase project resource name. When creating a FirebaseLink, you may provide this resource name using either a project number or project ID. Once this resource has been created, returned FirebaseLinks will always have a project_name that contains a project number. Format: 'projects/{project number}' Example: 'projects/1234'", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaGoogleAdsLink": {"description": "A link between a Google Analytics property and a Google Ads account.", "id": "GoogleAnalyticsAdminV1betaGoogleAdsLink", "properties": {"adsPersonalizationEnabled": {"description": "Enable personalized advertising features with this integration. Automatically publish my Google Analytics audience lists and Google Analytics remarketing events/parameters to the linked Google Ads account. If this field is not set on create/update, it will be defaulted to true.", "type": "boolean"}, "canManageClients": {"description": "Output only. If true, this link is for a Google Ads manager account.", "readOnly": true, "type": "boolean"}, "createTime": {"description": "Output only. Time when this link was originally created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creatorEmailAddress": {"description": "Output only. Email address of the user that created the link. An empty string will be returned if the email address can't be retrieved.", "readOnly": true, "type": "string"}, "customerId": {"description": "Immutable. Google Ads customer ID.", "type": "string"}, "name": {"description": "Output only. Format: properties/{propertyId}/googleAdsLinks/{googleAdsLinkId} Note: googleAdsLinkId is not the Google Ads customer ID.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time when this link was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaKeyEvent": {"description": "A key event in a Google Analytics property.", "id": "GoogleAnalyticsAdminV1betaKeyEvent", "properties": {"countingMethod": {"description": "Required. The method by which Key Events will be counted across multiple events within a session.", "enum": ["COUNTING_METHOD_UNSPECIFIED", "ONCE_PER_EVENT", "ONCE_PER_SESSION"], "enumDescriptions": ["Counting method not specified.", "Each Event instance is considered a Key Event.", "An Event instance is considered a Key Event at most once per session per user."], "type": "string"}, "createTime": {"description": "Output only. Time when this key event was created in the property.", "format": "google-datetime", "readOnly": true, "type": "string"}, "custom": {"description": "Output only. If set to true, this key event refers to a custom event. If set to false, this key event refers to a default event in GA. Default events typically have special meaning in GA. Default events are usually created for you by the GA system, but in some cases can be created by property admins. Custom events count towards the maximum number of custom key events that may be created per property.", "readOnly": true, "type": "boolean"}, "defaultValue": {"$ref": "GoogleAnalyticsAdminV1betaKeyEventDefaultValue", "description": "Optional. Defines a default value/currency for a key event."}, "deletable": {"description": "Output only. If set to true, this event can be deleted.", "readOnly": true, "type": "boolean"}, "eventName": {"description": "Immutable. The event name for this key event. Examples: 'click', 'purchase'", "type": "string"}, "name": {"description": "Output only. Resource name of this key event. Format: properties/{property}/keyEvents/{key_event}", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaKeyEventDefaultValue": {"description": "Defines a default value/currency for a key event.", "id": "GoogleAnalyticsAdminV1betaKeyEventDefaultValue", "properties": {"currencyCode": {"description": "Required. When an occurrence of this Key Event (specified by event_name) has no set currency this currency will be applied as the default. Must be in ISO 4217 currency code format. See https://en.wikipedia.org/wiki/ISO_4217 for more information.", "type": "string"}, "numericValue": {"description": "Required. This will be used to populate the \"value\" parameter for all occurrences of this Key Event (specified by event_name) where that parameter is unset.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaListAccountSummariesResponse": {"description": "Response message for ListAccountSummaries RPC.", "id": "GoogleAnalyticsAdminV1betaListAccountSummariesResponse", "properties": {"accountSummaries": {"description": "Account summaries of all accounts the caller has access to.", "items": {"$ref": "GoogleAnalyticsAdminV1betaAccountSummary"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaListAccountsResponse": {"description": "Request message for ListAccounts RPC.", "id": "GoogleAnalyticsAdminV1betaListAccountsResponse", "properties": {"accounts": {"description": "Results that were accessible to the caller.", "items": {"$ref": "GoogleAnalyticsAdminV1betaAccount"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaListConversionEventsResponse": {"description": "Response message for ListConversionEvents RPC.", "id": "GoogleAnalyticsAdminV1betaListConversionEventsResponse", "properties": {"conversionEvents": {"description": "The requested conversion events", "items": {"$ref": "GoogleAnalyticsAdminV1betaConversionEvent"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaListCustomDimensionsResponse": {"description": "Response message for ListCustomDimensions RPC.", "id": "GoogleAnalyticsAdminV1betaListCustomDimensionsResponse", "properties": {"customDimensions": {"description": "List of CustomDimensions.", "items": {"$ref": "GoogleAnalyticsAdminV1betaCustomDimension"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaListCustomMetricsResponse": {"description": "Response message for ListCustomMetrics RPC.", "id": "GoogleAnalyticsAdminV1betaListCustomMetricsResponse", "properties": {"customMetrics": {"description": "List of CustomMetrics.", "items": {"$ref": "GoogleAnalyticsAdminV1betaCustomMetric"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaListDataStreamsResponse": {"description": "Response message for ListDataStreams RPC.", "id": "GoogleAnalyticsAdminV1betaListDataStreamsResponse", "properties": {"dataStreams": {"description": "List of DataStreams.", "items": {"$ref": "GoogleAnalyticsAdminV1betaDataStream"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaListFirebaseLinksResponse": {"description": "Response message for ListFirebaseLinks RPC", "id": "GoogleAnalyticsAdminV1betaListFirebaseLinksResponse", "properties": {"firebaseLinks": {"description": "List of FirebaseLinks. This will have at most one value.", "items": {"$ref": "GoogleAnalyticsAdminV1betaFirebaseLink"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. Currently, Google Analytics supports only one FirebaseLink per property, so this will never be populated.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaListGoogleAdsLinksResponse": {"description": "Response message for ListGoogleAdsLinks RPC.", "id": "GoogleAnalyticsAdminV1betaListGoogleAdsLinksResponse", "properties": {"googleAdsLinks": {"description": "List of GoogleAdsLinks.", "items": {"$ref": "GoogleAnalyticsAdminV1betaGoogleAdsLink"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaListKeyEventsResponse": {"description": "Response message for ListKeyEvents RPC.", "id": "GoogleAnalyticsAdminV1betaListKeyEventsResponse", "properties": {"keyEvents": {"description": "The requested Key Events", "items": {"$ref": "GoogleAnalyticsAdminV1betaKeyEvent"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaListMeasurementProtocolSecretsResponse": {"description": "Response message for ListMeasurementProtocolSecret RPC", "id": "GoogleAnalyticsAdminV1betaListMeasurementProtocolSecretsResponse", "properties": {"measurementProtocolSecrets": {"description": "A list of secrets for the parent stream specified in the request.", "items": {"$ref": "GoogleAnalyticsAdminV1betaMeasurementProtocolSecret"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaListPropertiesResponse": {"description": "Response message for ListProperties RPC.", "id": "GoogleAnalyticsAdminV1betaListPropertiesResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "properties": {"description": "Results that matched the filter criteria and were accessible to the caller.", "items": {"$ref": "GoogleAnalyticsAdminV1betaProperty"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaMeasurementProtocolSecret": {"description": "A secret value used for sending hits to Measurement Protocol.", "id": "GoogleAnalyticsAdminV1betaMeasurementProtocolSecret", "properties": {"displayName": {"description": "Required. Human-readable display name for this secret.", "type": "string"}, "name": {"description": "Output only. Resource name of this secret. This secret may be a child of any type of stream. Format: properties/{property}/dataStreams/{dataStream}/measurementProtocolSecrets/{measurementProtocolSecret}", "readOnly": true, "type": "string"}, "secretValue": {"description": "Output only. The measurement protocol secret value. Pass this value to the api_secret field of the Measurement Protocol API when sending hits to this secret's parent property.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaNumericValue": {"description": "To represent a number.", "id": "GoogleAnalyticsAdminV1betaNumericValue", "properties": {"doubleValue": {"description": "Double value", "format": "double", "type": "number"}, "int64Value": {"description": "Integer value", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaProperty": {"description": "A resource message representing a Google Analytics property.", "id": "GoogleAnalyticsAdminV1betaProperty", "properties": {"account": {"description": "Immutable. The resource name of the parent account Format: accounts/{account_id} Example: \"accounts/123\"", "type": "string"}, "createTime": {"description": "Output only. Time when the entity was originally created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "currencyCode": {"description": "The currency type used in reports involving monetary values. Format: https://en.wikipedia.org/wiki/ISO_4217 Examples: \"USD\", \"EUR\", \"JPY\"", "type": "string"}, "deleteTime": {"description": "Output only. If set, the time at which this property was trashed. If not set, then this property is not currently in the trash can.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Required. Human-readable display name for this property. The max allowed display name length is 100 UTF-16 code units.", "type": "string"}, "expireTime": {"description": "Output only. If set, the time at which this trashed property will be permanently deleted. If not set, then this property is not currently in the trash can and is not slated to be deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "industryCategory": {"description": "Industry associated with this property Example: AUTOMOTIVE, FOOD_AND_DRINK", "enum": ["INDUSTRY_CATEGORY_UNSPECIFIED", "AUTOMOTIVE", "BUSINESS_AND_INDUSTRIAL_MARKETS", "FINANCE", "HEALTHCARE", "TECHNOLOGY", "TRAVEL", "OTHER", "ARTS_AND_ENTERTAINMENT", "BEAUTY_AND_FITNESS", "BOOKS_AND_LITERATURE", "FOOD_AND_DRINK", "GAMES", "HOBBIES_AND_LEISURE", "HOME_AND_GARDEN", "INTERNET_AND_TELECOM", "LAW_AND_GOVERNMENT", "NEWS", "ONLINE_COMMUNITIES", "PEOPLE_AND_SOCIETY", "PETS_AND_ANIMALS", "REAL_ESTATE", "REFERENCE", "SCIENCE", "SPORTS", "JOBS_AND_EDUCATION", "SHOPPING"], "enumDescriptions": ["Industry category unspecified", "Automotive", "Business and industrial markets", "Finance", "Healthcare", "Technology", "Travel", "Other", "Arts and entertainment", "Beauty and fitness", "Books and literature", "Food and drink", "Games", "Hobbies and leisure", "Home and garden", "Internet and telecom", "Law and government", "News", "Online communities", "People and society", "Pets and animals", "Real estate", "Reference", "Science", "Sports", "Jobs and education", "Shopping"], "type": "string"}, "name": {"description": "Output only. Resource name of this property. Format: properties/{property_id} Example: \"properties/1000\"", "readOnly": true, "type": "string"}, "parent": {"description": "Immutable. Resource name of this property's logical parent. Note: The Property-Moving UI can be used to change the parent. Format: accounts/{account}, properties/{property} Example: \"accounts/100\", \"properties/101\"", "type": "string"}, "propertyType": {"description": "Immutable. The property type for this Property resource. When creating a property, if the type is \"PROPERTY_TYPE_UNSPECIFIED\", then \"ORDINARY_PROPERTY\" will be implied.", "enum": ["PROPERTY_TYPE_UNSPECIFIED", "PROPERTY_TYPE_ORDINARY", "PROPERTY_TYPE_SUBPROPERTY", "PROPERTY_TYPE_ROLLUP"], "enumDescriptions": ["Unknown or unspecified property type", "Ordinary Google Analytics property", "Google Analytics subproperty", "Google Analytics rollup property"], "type": "string"}, "serviceLevel": {"description": "Output only. The Google Analytics service level that applies to this property.", "enum": ["SERVICE_LEVEL_UNSPECIFIED", "GOOGLE_ANALYTICS_STANDARD", "GOOGLE_ANALYTICS_360"], "enumDescriptions": ["Service level not specified or invalid.", "The standard version of Google Analytics.", "The paid, premium version of Google Analytics."], "readOnly": true, "type": "string"}, "timeZone": {"description": "Required. Reporting Time Zone, used as the day boundary for reports, regardless of where the data originates. If the time zone honors DST, Analytics will automatically adjust for the changes. NOTE: Changing the time zone only affects data going forward, and is not applied retroactively. Format: https://www.iana.org/time-zones Example: \"America/Los_Angeles\"", "type": "string"}, "updateTime": {"description": "Output only. Time when entity payload fields were last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaPropertySummary": {"description": "A virtual resource representing metadata for a Google Analytics property.", "id": "GoogleAnalyticsAdminV1betaPropertySummary", "properties": {"displayName": {"description": "Display name for the property referred to in this property summary.", "type": "string"}, "parent": {"description": "Resource name of this property's logical parent. Note: The Property-Moving UI can be used to change the parent. Format: accounts/{account}, properties/{property} Example: \"accounts/100\", \"properties/200\"", "type": "string"}, "property": {"description": "Resource name of property referred to by this property summary Format: properties/{property_id} Example: \"properties/1000\"", "type": "string"}, "propertyType": {"description": "The property's property type.", "enum": ["PROPERTY_TYPE_UNSPECIFIED", "PROPERTY_TYPE_ORDINARY", "PROPERTY_TYPE_SUBPROPERTY", "PROPERTY_TYPE_ROLLUP"], "enumDescriptions": ["Unknown or unspecified property type", "Ordinary Google Analytics property", "Google Analytics subproperty", "Google Analytics rollup property"], "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaProvisionAccountTicketRequest": {"description": "Request message for ProvisionAccountTicket RPC.", "id": "GoogleAnalyticsAdminV1betaProvisionAccountTicketRequest", "properties": {"account": {"$ref": "GoogleAnalyticsAdminV1betaAccount", "description": "The account to create."}, "redirectUri": {"description": "Redirect URI where the user will be sent after accepting Terms of Service. Must be configured in Cloud Console as a Redirect URI.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaProvisionAccountTicketResponse": {"description": "Response message for ProvisionAccountTicket RPC.", "id": "GoogleAnalyticsAdminV1betaProvisionAccountTicketResponse", "properties": {"accountTicketId": {"description": "The param to be passed in the ToS link.", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaRunAccessReportRequest": {"description": "The request for a Data Access Record Report.", "id": "GoogleAnalyticsAdminV1betaRunAccessReportRequest", "properties": {"dateRanges": {"description": "Date ranges of access records to read. If multiple date ranges are requested, each response row will contain a zero based date range index. If two date ranges overlap, the access records for the overlapping days is included in the response rows for both date ranges. Requests are allowed up to 2 date ranges.", "items": {"$ref": "GoogleAnalyticsAdminV1betaAccessDateRange"}, "type": "array"}, "dimensionFilter": {"$ref": "GoogleAnalyticsAdminV1betaAccessFilterExpression", "description": "Dimension filters let you restrict report response to specific dimension values which match the filter. For example, filtering on access records of a single user. To learn more, see [Fundamentals of Dimension Filters](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#dimension_filters) for examples. Metrics cannot be used in this filter."}, "dimensions": {"description": "The dimensions requested and displayed in the response. Requests are allowed up to 9 dimensions.", "items": {"$ref": "GoogleAnalyticsAdminV1betaAccessDimension"}, "type": "array"}, "expandGroups": {"description": "Optional. Decides whether to return the users within user groups. This field works only when include_all_users is set to true. If true, it will return all users with access to the specified property or account. If false, only the users with direct access will be returned.", "type": "boolean"}, "includeAllUsers": {"description": "Optional. Determines whether to include users who have never made an API call in the response. If true, all users with access to the specified property or account are included in the response, regardless of whether they have made an API call or not. If false, only the users who have made an API call will be included.", "type": "boolean"}, "limit": {"description": "The number of rows to return. If unspecified, 10,000 rows are returned. The API returns a maximum of 100,000 rows per request, no matter how many you ask for. `limit` must be positive. The API may return fewer rows than the requested `limit`, if there aren't as many remaining rows as the `limit`. For instance, there are fewer than 300 possible values for the dimension `country`, so when reporting on only `country`, you can't get more than 300 rows, even if you set `limit` to a higher value. To learn more about this pagination parameter, see [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).", "format": "int64", "type": "string"}, "metricFilter": {"$ref": "GoogleAnalyticsAdminV1betaAccessFilterExpression", "description": "Metric filters allow you to restrict report response to specific metric values which match the filter. Metric filters are applied after aggregating the report's rows, similar to SQL having-clause. Dimensions cannot be used in this filter."}, "metrics": {"description": "The metrics requested and displayed in the response. Requests are allowed up to 10 metrics.", "items": {"$ref": "GoogleAnalyticsAdminV1betaAccessMetric"}, "type": "array"}, "offset": {"description": "The row count of the start row. The first row is counted as row 0. If offset is unspecified, it is treated as 0. If offset is zero, then this method will return the first page of results with `limit` entries. To learn more about this pagination parameter, see [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).", "format": "int64", "type": "string"}, "orderBys": {"description": "Specifies how rows are ordered in the response.", "items": {"$ref": "GoogleAnalyticsAdminV1betaAccessOrderBy"}, "type": "array"}, "returnEntityQuota": {"description": "Toggles whether to return the current state of this Analytics Property's quota. Quota is returned in [AccessQuota](#AccessQuota). For account-level requests, this field must be false.", "type": "boolean"}, "timeZone": {"description": "This request's time zone if specified. If unspecified, the property's time zone is used. The request's time zone is used to interpret the start & end dates of the report. Formatted as strings from the IANA Time Zone database (https://www.iana.org/time-zones); for example \"America/New_York\" or \"Asia/Tokyo\".", "type": "string"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaRunAccessReportResponse": {"description": "The customized Data Access Record Report response.", "id": "GoogleAnalyticsAdminV1betaRunAccessReportResponse", "properties": {"dimensionHeaders": {"description": "The header for a column in the report that corresponds to a specific dimension. The number of DimensionHeaders and ordering of DimensionHeaders matches the dimensions present in rows.", "items": {"$ref": "GoogleAnalyticsAdminV1betaAccessDimensionHeader"}, "type": "array"}, "metricHeaders": {"description": "The header for a column in the report that corresponds to a specific metric. The number of MetricHeaders and ordering of MetricHeaders matches the metrics present in rows.", "items": {"$ref": "GoogleAnalyticsAdminV1betaAccessMetricHeader"}, "type": "array"}, "quota": {"$ref": "GoogleAnalyticsAdminV1betaAccessQuota", "description": "The quota state for this Analytics property including this request. This field doesn't work with account-level requests."}, "rowCount": {"description": "The total number of rows in the query result. `rowCount` is independent of the number of rows returned in the response, the `limit` request parameter, and the `offset` request parameter. For example if a query returns 175 rows and includes `limit` of 50 in the API request, the response will contain `rowCount` of 175 but only 50 rows. To learn more about this pagination parameter, see [Pagination](https://developers.google.com/analytics/devguides/reporting/data/v1/basics#pagination).", "format": "int32", "type": "integer"}, "rows": {"description": "Rows of dimension value combinations and metric values in the report.", "items": {"$ref": "GoogleAnalyticsAdminV1betaAccessRow"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaSearchChangeHistoryEventsRequest": {"description": "Request message for SearchChangeHistoryEvents RPC.", "id": "GoogleAnalyticsAdminV1betaSearchChangeHistoryEventsRequest", "properties": {"action": {"description": "Optional. If set, only return changes that match one or more of these types of actions.", "items": {"enum": ["ACTION_TYPE_UNSPECIFIED", "CREATED", "UPDATED", "DELETED"], "enumDescriptions": ["Action type unknown or not specified.", "Resource was created in this change.", "Resource was updated in this change.", "Resource was deleted in this change."], "type": "string"}, "type": "array"}, "actorEmail": {"description": "Optional. If set, only return changes if they are made by a user in this list.", "items": {"type": "string"}, "type": "array"}, "earliestChangeTime": {"description": "Optional. If set, only return changes made after this time (inclusive).", "format": "google-datetime", "type": "string"}, "latestChangeTime": {"description": "Optional. If set, only return changes made before this time (inclusive).", "format": "google-datetime", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of ChangeHistoryEvent items to return. If unspecified, at most 50 items will be returned. The maximum value is 200 (higher values will be coerced to the maximum). Note that the service may return a page with fewer items than this value specifies (potentially even zero), and that there still may be additional pages. If you want a particular number of items, you'll need to continue requesting additional pages using `page_token` until you get the needed number.", "format": "int32", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `SearchChangeHistoryEvents` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `SearchChangeHistoryEvents` must match the call that provided the page token.", "type": "string"}, "property": {"description": "Optional. Resource name for a child property. If set, only return changes made to this property or its child resources. Format: properties/{propertyId} Example: `properties/100`", "type": "string"}, "resourceType": {"description": "Optional. If set, only return changes if they are for a resource that matches at least one of these types.", "items": {"enum": ["CHANGE_HISTORY_RESOURCE_TYPE_UNSPECIFIED", "ACCOUNT", "PROPERTY", "FIREBASE_LINK", "GOOGLE_ADS_LINK", "GOOGLE_SIGNALS_SETTINGS", "CONVERSION_EVENT", "MEASUREMENT_PROTOCOL_SECRET", "CUSTOM_DIMENSION", "CUSTOM_METRIC", "DATA_RETENTION_SETTINGS", "DISPLAY_VIDEO_360_ADVERTISER_LINK", "DISPLAY_VIDEO_360_ADVERTISER_LINK_PROPOSAL", "DATA_STREAM", "ATTRIBUTION_SETTINGS"], "enumDescriptions": ["Resource type unknown or not specified.", "Account resource", "Property resource", "FirebaseLink resource", "GoogleAdsLink resource", "GoogleSignalsSettings resource", "ConversionEvent resource", "MeasurementProtocolSecret resource", "CustomDimension resource", "CustomMetric resource", "DataRetentionSettings resource", "DisplayVideo360AdvertiserLink resource", "DisplayVideo360AdvertiserLinkProposal resource", "DataStream resource", "AttributionSettings resource"], "type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAnalyticsAdminV1betaSearchChangeHistoryEventsResponse": {"description": "Response message for SearchAccounts RPC.", "id": "GoogleAnalyticsAdminV1betaSearchChangeHistoryEventsResponse", "properties": {"changeHistoryEvents": {"description": "Results that were accessible to the caller.", "items": {"$ref": "GoogleAnalyticsAdminV1betaChangeHistoryEvent"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}}, "servicePath": "", "title": "Google Analytics Admin API", "version": "v1beta", "version_module": true}