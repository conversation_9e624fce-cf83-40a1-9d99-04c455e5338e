{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://firebaseml.googleapis.com/", "batchPath": "batch", "canonicalName": "Firebase ML", "description": "Access custom machine learning models hosted via Firebase ML.", "discoveryVersion": "v1", "documentationLink": "https://firebase.google.com", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "firebaseml:v1beta2", "kind": "discovery#restDescription", "mtlsRootUrl": "https://firebaseml.mtls.googleapis.com/", "name": "firebaseml", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"models": {"methods": {"create": {"description": "Creates a model in Firebase ML. The longrunning operation will eventually return a Model", "flatPath": "v1beta2/projects/{projectsId}/models", "httpMethod": "POST", "id": "firebaseml.projects.models.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent project resource where the model is to be created. The parent must have the form `projects/{project_id}`", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+parent}/models", "request": {"$ref": "Model"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a model", "flatPath": "v1beta2/projects/{projectsId}/models/{modelsId}", "httpMethod": "DELETE", "id": "firebaseml.projects.models.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the model to delete. The name must have the form `projects/{project_id}/models/{model_id}`", "location": "path", "pattern": "^projects/[^/]+/models/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "download": {"description": "Gets Download information for a model. This is meant for downloading model resources onto devices. It gives very limited information about the model.", "flatPath": "v1beta2/projects/{projectsId}/models/{modelsId}:download", "httpMethod": "GET", "id": "firebaseml.projects.models.download", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the model to download. The name must have the form `projects/{project}/models/{model}`", "location": "path", "pattern": "^projects/[^/]+/models/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:download", "response": {"$ref": "DownloadModelResponse"}}, "get": {"description": "Gets a model resource.", "flatPath": "v1beta2/projects/{projectsId}/models/{modelsId}", "httpMethod": "GET", "id": "firebaseml.projects.models.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the model to get. The name must have the form `projects/{project_id}/models/{model_id}`", "location": "path", "pattern": "^projects/[^/]+/models/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "Model"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists the models", "flatPath": "v1beta2/projects/{projectsId}/models", "httpMethod": "GET", "id": "firebaseml.projects.models.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter for the list e.g. 'tags: abc' to list models which are tagged with \"abc\"", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of items to return", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the parent to list models for. The parent must have the form `projects/{project_id}'", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+parent}/models", "response": {"$ref": "ListModelsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a model. The longrunning operation will eventually return a Model.", "flatPath": "v1beta2/projects/{projectsId}/models/{modelsId}", "httpMethod": "PATCH", "id": "firebaseml.projects.models.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the Model. Model names have the form `projects/{project_id}/models/{model_id}` The name is ignored when creating a model.", "location": "path", "pattern": "^projects/[^/]+/models/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The update mask", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "Model"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta2/projects/{projectsId}/operations/{operationsId}", "httpMethod": "GET", "id": "firebaseml.projects.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}, "revision": "20240705", "rootUrl": "https://firebaseml.googleapis.com/", "schemas": {"DownloadModelResponse": {"description": "The response for downloading a model to device.", "id": "DownloadModelResponse", "properties": {"downloadUri": {"description": "Output only. A download URI for the model/zip file.", "readOnly": true, "type": "string"}, "expireTime": {"description": "Output only. The time that the download URI link expires. If the link has expired, the REST call must be repeated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "modelFormat": {"description": "Output only. The format of the model being downloaded.", "enum": ["MODEL_FORMAT_UNSPECIFIED", "TFLITE"], "enumDescriptions": ["Unknown format", "TFLite model"], "readOnly": true, "type": "string"}, "sizeBytes": {"description": "Output only. The size of the file(s), if this information is available.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "ListModelsResponse": {"description": "The response for list models", "id": "ListModelsResponse", "properties": {"models": {"description": "The list of models", "items": {"$ref": "Model"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}}, "type": "object"}, "Model": {"description": "An ML model hosted in Firebase ML", "id": "Model", "properties": {"activeOperations": {"description": "Output only. Lists operation ids associated with this model whose status is NOT done.", "items": {"$ref": "Operation"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. Timestamp when this model was created in Firebase ML.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Required. The name of the model to create. The name can be up to 32 characters long and can consist only of ASCII Latin letters A-Z and a-z, underscores(_) and ASCII digits 0-9. It must start with a letter.", "type": "string"}, "etag": {"description": "Output only. See RFC7232 https://tools.ietf.org/html/rfc7232#section-2.3", "readOnly": true, "type": "string"}, "modelHash": {"description": "Output only. The model_hash will change if a new file is available for download.", "readOnly": true, "type": "string"}, "name": {"description": "The resource name of the Model. Model names have the form `projects/{project_id}/models/{model_id}` The name is ignored when creating a model.", "type": "string"}, "state": {"$ref": "ModelState", "description": "State common to all model types. Includes publishing and validation information."}, "tags": {"description": "User defined tags which can be used to group/filter models during listing", "items": {"type": "string"}, "type": "array"}, "tfliteModel": {"$ref": "TfLiteModel", "description": "A TFLite Model"}, "updateTime": {"description": "Output only. Timestamp when this model was updated in Firebase ML.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ModelOperationMetadata": {"description": "This is returned in the longrunning operations for create/update.", "id": "ModelOperationMetadata", "properties": {"basicOperationStatus": {"enum": ["BASIC_OPERATION_STATUS_UNSPECIFIED", "BASIC_OPERATION_STATUS_UPLOADING", "BASIC_OPERATION_STATUS_VERIFYING"], "enumDescriptions": ["The status is unspecified", "The model file is being uploaded", "The model file is being verified"], "type": "string"}, "name": {"description": "The name of the model we are creating/updating The name must have the form `projects/{project_id}/models/{model_id}`", "type": "string"}}, "type": "object"}, "ModelState": {"description": "State common to all model types. Includes publishing and validation information.", "id": "ModelState", "properties": {"published": {"description": "Indicates if this model has been published.", "type": "boolean"}, "validationError": {"$ref": "Status", "description": "Output only. Indicates the latest validation error on the model if any. A model may have validation errors if there were problems during the model creation/update. e.g. in the case of a TfLiteModel, if a tflite model file was missing or in the wrong format. This field will be empty for valid models.", "readOnly": true}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "TfLiteModel": {"description": "Information that is specific to TfLite models.", "id": "TfLiteModel", "properties": {"automlModel": {"description": "The AutoML model id referencing a model you created with the AutoML API. The name should have format 'projects//locations//models/' (This is the model resource name returned from the AutoML API)", "type": "string"}, "gcsTfliteUri": {"description": "The TfLite file containing the model. (Stored in Google Cloud). The gcs_tflite_uri should have form: gs://some-bucket/some-model.tflite Note: If you update the file in the original location, it is necessary to call UpdateModel for ML to pick up and validate the updated file.", "type": "string"}, "sizeBytes": {"description": "Output only. The size of the TFLite model", "readOnly": true, "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Firebase ML API", "version": "v1beta2", "version_module": true}