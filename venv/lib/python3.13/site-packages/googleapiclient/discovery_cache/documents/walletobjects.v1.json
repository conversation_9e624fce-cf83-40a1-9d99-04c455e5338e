{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/wallet_object.issuer": {"description": "Private Service: https://www.googleapis.com/auth/wallet_object.issuer"}}}}, "basePath": "", "baseUrl": "https://walletobjects.googleapis.com/", "batchPath": "batch", "canonicalName": "Walletobjects", "description": "API for issuers to save and manage Google Wallet Objects.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/pay/passes", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "walletobjects:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://walletobjects.mtls.googleapis.com/", "name": "walletobjects", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"eventticketclass": {"methods": {"addmessage": {"description": "Adds a message to the event ticket class referenced by the given class ID.", "flatPath": "walletobjects/v1/eventTicketClass/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.eventticketclass.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/eventTicketClass/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "EventTicketClassAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the event ticket class with the given class ID.", "flatPath": "walletobjects/v1/eventTicketClass/{resourceId}", "httpMethod": "GET", "id": "walletobjects.eventticketclass.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/eventTicketClass/{resourceId}", "response": {"$ref": "EventTicketClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts an event ticket class with the given ID and properties.", "flatPath": "walletobjects/v1/eventTicketClass", "httpMethod": "POST", "id": "walletobjects.eventticketclass.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/eventTicketClass", "request": {"$ref": "EventTicketClass"}, "response": {"$ref": "EventTicketClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all event ticket classes for a given issuer ID.", "flatPath": "walletobjects/v1/eventTicketClass", "httpMethod": "GET", "id": "walletobjects.eventticketclass.list", "parameterOrder": [], "parameters": {"issuerId": {"description": "The ID of the issuer authorized to list classes.", "format": "int64", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` classes are available in a list. For example, if you have a list of 200 classes and you call list with `maxResults` set to 20, list will return the first 20 classes and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 classes.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/eventTicketClass", "response": {"$ref": "EventTicketClassListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the event ticket class referenced by the given class ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/eventTicketClass/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.eventticketclass.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/eventTicketClass/{resourceId}", "request": {"$ref": "EventTicketClass"}, "response": {"$ref": "EventTicketClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the event ticket class referenced by the given class ID.", "flatPath": "walletobjects/v1/eventTicketClass/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.eventticketclass.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/eventTicketClass/{resourceId}", "request": {"$ref": "EventTicketClass"}, "response": {"$ref": "EventTicketClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "eventticketobject": {"methods": {"addmessage": {"description": "Adds a message to the event ticket object referenced by the given object ID.", "flatPath": "walletobjects/v1/eventTicketObject/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.eventticketobject.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/eventTicketObject/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "EventTicketObjectAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the event ticket object with the given object ID.", "flatPath": "walletobjects/v1/eventTicketObject/{resourceId}", "httpMethod": "GET", "id": "walletobjects.eventticketobject.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/eventTicketObject/{resourceId}", "response": {"$ref": "EventTicketObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts an event ticket object with the given ID and properties.", "flatPath": "walletobjects/v1/eventTicketObject", "httpMethod": "POST", "id": "walletobjects.eventticketobject.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/eventTicketObject", "request": {"$ref": "EventTicketObject"}, "response": {"$ref": "EventTicketObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all event ticket objects for a given issuer ID.", "flatPath": "walletobjects/v1/eventTicketObject", "httpMethod": "GET", "id": "walletobjects.eventticketobject.list", "parameterOrder": [], "parameters": {"classId": {"description": "The ID of the class whose objects will be listed.", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` objects are available in a list. For example, if you have a list of 200 objects and you call list with `maxResults` set to 20, list will return the first 20 objects and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 objects.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/eventTicketObject", "response": {"$ref": "EventTicketObjectListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "modifylinkedofferobjects": {"description": "Modifies linked offer objects for the event ticket object with the given ID.", "flatPath": "walletobjects/v1/eventTicketObject/{resourceId}/modifyLinkedOfferObjects", "httpMethod": "POST", "id": "walletobjects.eventticketobject.modifylinkedofferobjects", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/eventTicketObject/{resourceId}/modifyLinkedOfferObjects", "request": {"$ref": "ModifyLinkedOfferObjectsRequest"}, "response": {"$ref": "EventTicketObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the event ticket object referenced by the given object ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/eventTicketObject/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.eventticketobject.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/eventTicketObject/{resourceId}", "request": {"$ref": "EventTicketObject"}, "response": {"$ref": "EventTicketObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the event ticket object referenced by the given object ID.", "flatPath": "walletobjects/v1/eventTicketObject/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.eventticketobject.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/eventTicketObject/{resourceId}", "request": {"$ref": "EventTicketObject"}, "response": {"$ref": "EventTicketObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "flightclass": {"methods": {"addmessage": {"description": "Adds a message to the flight class referenced by the given class ID.", "flatPath": "walletobjects/v1/flightClass/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.flightclass.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/flightClass/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "FlightClassAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the flight class with the given class ID.", "flatPath": "walletobjects/v1/flightClass/{resourceId}", "httpMethod": "GET", "id": "walletobjects.flightclass.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/flightClass/{resourceId}", "response": {"$ref": "FlightClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts an flight class with the given ID and properties.", "flatPath": "walletobjects/v1/flightClass", "httpMethod": "POST", "id": "walletobjects.flightclass.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/flightClass", "request": {"$ref": "FlightClass"}, "response": {"$ref": "FlightClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all flight classes for a given issuer ID.", "flatPath": "walletobjects/v1/flightClass", "httpMethod": "GET", "id": "walletobjects.flightclass.list", "parameterOrder": [], "parameters": {"issuerId": {"description": "The ID of the issuer authorized to list classes.", "format": "int64", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` classes are available in a list. For example, if you have a list of 200 classes and you call list with `maxResults` set to 20, list will return the first 20 classes and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 classes.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/flightClass", "response": {"$ref": "FlightClassListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the flight class referenced by the given class ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/flightClass/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.flightclass.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/flightClass/{resourceId}", "request": {"$ref": "FlightClass"}, "response": {"$ref": "FlightClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the flight class referenced by the given class ID.", "flatPath": "walletobjects/v1/flightClass/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.flightclass.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/flightClass/{resourceId}", "request": {"$ref": "FlightClass"}, "response": {"$ref": "FlightClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "flightobject": {"methods": {"addmessage": {"description": "Adds a message to the flight object referenced by the given object ID.", "flatPath": "walletobjects/v1/flightObject/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.flightobject.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/flightObject/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "FlightObjectAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the flight object with the given object ID.", "flatPath": "walletobjects/v1/flightObject/{resourceId}", "httpMethod": "GET", "id": "walletobjects.flightobject.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/flightObject/{resourceId}", "response": {"$ref": "FlightObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts an flight object with the given ID and properties.", "flatPath": "walletobjects/v1/flightObject", "httpMethod": "POST", "id": "walletobjects.flightobject.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/flightObject", "request": {"$ref": "FlightObject"}, "response": {"$ref": "FlightObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all flight objects for a given issuer ID.", "flatPath": "walletobjects/v1/flightObject", "httpMethod": "GET", "id": "walletobjects.flightobject.list", "parameterOrder": [], "parameters": {"classId": {"description": "The ID of the class whose objects will be listed.", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` objects are available in a list. For example, if you have a list of 200 objects and you call list with `maxResults` set to 20, list will return the first 20 objects and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 objects.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/flightObject", "response": {"$ref": "FlightObjectListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the flight object referenced by the given object ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/flightObject/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.flightobject.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/flightObject/{resourceId}", "request": {"$ref": "FlightObject"}, "response": {"$ref": "FlightObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the flight object referenced by the given object ID.", "flatPath": "walletobjects/v1/flightObject/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.flightobject.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/flightObject/{resourceId}", "request": {"$ref": "FlightObject"}, "response": {"$ref": "FlightObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "genericclass": {"methods": {"addmessage": {"description": "Adds a message to the generic class referenced by the given class ID.", "flatPath": "walletobjects/v1/genericClass/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.genericclass.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/genericClass/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "GenericClassAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the generic class with the given class ID.", "flatPath": "walletobjects/v1/genericClass/{resourceId}", "httpMethod": "GET", "id": "walletobjects.genericclass.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value needs to follow the format `issuerID.identifier` where `issuerID` is issued by Google and `identifier` is chosen by you. The unique identifier can only include alphanumeric characters, `.`, `_`, or `-`.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/genericClass/{resourceId}", "response": {"$ref": "GenericClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts a generic class with the given ID and properties.", "flatPath": "walletobjects/v1/genericClass", "httpMethod": "POST", "id": "walletobjects.genericclass.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/genericClass", "request": {"$ref": "GenericClass"}, "response": {"$ref": "GenericClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all generic classes for a given issuer ID.", "flatPath": "walletobjects/v1/genericClass", "httpMethod": "GET", "id": "walletobjects.genericclass.list", "parameterOrder": [], "parameters": {"issuerId": {"description": "The ID of the issuer authorized to list classes.", "format": "int64", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` classes are available in a list. For example, if you have a list of 200 classes and you call list with `maxResults` set to 20, list will return the first 20 classes and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 classes.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/genericClass", "response": {"$ref": "GenericClassListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the generic class referenced by the given class ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/genericClass/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.genericclass.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value needs to follow the format `issuerID.identifier` where `issuerID` is issued by Google and `identifier` is chosen by you. The unique identifier can only include alphanumeric characters, `.`, `_`, or `-`.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/genericClass/{resourceId}", "request": {"$ref": "GenericClass"}, "response": {"$ref": "GenericClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the Generic class referenced by the given class ID.", "flatPath": "walletobjects/v1/genericClass/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.genericclass.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value needs to follow the format `issuerID.identifier` where `issuerID` is issued by Google and `identifier` is chosen by you. The unique identifier can only include alphanumeric characters, `.`, `_`, or `-`.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/genericClass/{resourceId}", "request": {"$ref": "GenericClass"}, "response": {"$ref": "GenericClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "genericobject": {"methods": {"addmessage": {"description": "Adds a message to the generic object referenced by the given object ID.", "flatPath": "walletobjects/v1/genericObject/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.genericobject.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/genericObject/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "GenericObjectAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the generic object with the given object ID.", "flatPath": "walletobjects/v1/genericObject/{resourceId}", "httpMethod": "GET", "id": "walletobjects.genericobject.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value needs to follow the format `issuerID.identifier` where `issuerID` is issued by Google and `identifier` is chosen by you. The unique identifier can only include alphanumeric characters, `.`, `_`, or `-`.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/genericObject/{resourceId}", "response": {"$ref": "GenericObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts a generic object with the given ID and properties.", "flatPath": "walletobjects/v1/genericObject", "httpMethod": "POST", "id": "walletobjects.genericobject.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/genericObject", "request": {"$ref": "GenericObject"}, "response": {"$ref": "GenericObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all generic objects for a given issuer ID.", "flatPath": "walletobjects/v1/genericObject", "httpMethod": "GET", "id": "walletobjects.genericobject.list", "parameterOrder": [], "parameters": {"classId": {"description": "The ID of the class whose objects will be listed.", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` objects are available in a list. For example, if you have a list of 200 objects and you call list with `maxResults` set to 20, list will return the first 20 objects and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 objects.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/genericObject", "response": {"$ref": "GenericObjectListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the generic object referenced by the given object ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/genericObject/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.genericobject.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value needs to follow the format `issuerID.identifier` where `issuerID` is issued by Google and `identifier` is chosen by you. The unique identifier can only include alphanumeric characters, `.`, `_`, or `-`.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/genericObject/{resourceId}", "request": {"$ref": "GenericObject"}, "response": {"$ref": "GenericObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the generic object referenced by the given object ID.", "flatPath": "walletobjects/v1/genericObject/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.genericobject.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value needs to follow the format `issuerID.identifier` where `issuerID` is issued by Google and `identifier` is chosen by you. The unique identifier can only include alphanumeric characters, `.`, `_`, or `-`.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/genericObject/{resourceId}", "request": {"$ref": "GenericObject"}, "response": {"$ref": "GenericObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "giftcardclass": {"methods": {"addmessage": {"description": "Adds a message to the gift card class referenced by the given class ID.", "flatPath": "walletobjects/v1/giftCardClass/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.giftcardclass.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/giftCardClass/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "GiftCardClassAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the gift card class with the given class ID.", "flatPath": "walletobjects/v1/giftCardClass/{resourceId}", "httpMethod": "GET", "id": "walletobjects.giftcardclass.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/giftCardClass/{resourceId}", "response": {"$ref": "GiftCardClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts an gift card class with the given ID and properties.", "flatPath": "walletobjects/v1/giftCardClass", "httpMethod": "POST", "id": "walletobjects.giftcardclass.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/giftCardClass", "request": {"$ref": "GiftCardClass"}, "response": {"$ref": "GiftCardClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all gift card classes for a given issuer ID.", "flatPath": "walletobjects/v1/giftCardClass", "httpMethod": "GET", "id": "walletobjects.giftcardclass.list", "parameterOrder": [], "parameters": {"issuerId": {"description": "The ID of the issuer authorized to list classes.", "format": "int64", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` classes are available in a list. For example, if you have a list of 200 classes and you call list with `maxResults` set to 20, list will return the first 20 classes and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 classes.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/giftCardClass", "response": {"$ref": "GiftCardClassListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the gift card class referenced by the given class ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/giftCardClass/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.giftcardclass.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/giftCardClass/{resourceId}", "request": {"$ref": "GiftCardClass"}, "response": {"$ref": "GiftCardClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the gift card class referenced by the given class ID.", "flatPath": "walletobjects/v1/giftCardClass/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.giftcardclass.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/giftCardClass/{resourceId}", "request": {"$ref": "GiftCardClass"}, "response": {"$ref": "GiftCardClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "giftcardobject": {"methods": {"addmessage": {"description": "Adds a message to the gift card object referenced by the given object ID.", "flatPath": "walletobjects/v1/giftCardObject/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.giftcardobject.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/giftCardObject/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "GiftCardObjectAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the gift card object with the given object ID.", "flatPath": "walletobjects/v1/giftCardObject/{resourceId}", "httpMethod": "GET", "id": "walletobjects.giftcardobject.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/giftCardObject/{resourceId}", "response": {"$ref": "GiftCardObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts an gift card object with the given ID and properties.", "flatPath": "walletobjects/v1/giftCardObject", "httpMethod": "POST", "id": "walletobjects.giftcardobject.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/giftCardObject", "request": {"$ref": "GiftCardObject"}, "response": {"$ref": "GiftCardObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all gift card objects for a given issuer ID.", "flatPath": "walletobjects/v1/giftCardObject", "httpMethod": "GET", "id": "walletobjects.giftcardobject.list", "parameterOrder": [], "parameters": {"classId": {"description": "The ID of the class whose objects will be listed.", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` objects are available in a list. For example, if you have a list of 200 objects and you call list with `maxResults` set to 20, list will return the first 20 objects and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 objects.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/giftCardObject", "response": {"$ref": "GiftCardObjectListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the gift card object referenced by the given object ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/giftCardObject/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.giftcardobject.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/giftCardObject/{resourceId}", "request": {"$ref": "GiftCardObject"}, "response": {"$ref": "GiftCardObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the gift card object referenced by the given object ID.", "flatPath": "walletobjects/v1/giftCardObject/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.giftcardobject.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/giftCardObject/{resourceId}", "request": {"$ref": "GiftCardObject"}, "response": {"$ref": "GiftCardObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "issuer": {"methods": {"get": {"description": "Returns the issuer with the given issuer ID.", "flatPath": "walletobjects/v1/issuer/{resourceId}", "httpMethod": "GET", "id": "walletobjects.issuer.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an issuer.", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/issuer/{resourceId}", "response": {"$ref": "Issuer"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts an issuer with the given ID and properties.", "flatPath": "walletobjects/v1/issuer", "httpMethod": "POST", "id": "walletobjects.issuer.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/issuer", "request": {"$ref": "Issuer"}, "response": {"$ref": "Issuer"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all issuers shared to the caller.", "flatPath": "walletobjects/v1/issuer", "httpMethod": "GET", "id": "walletobjects.issuer.list", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/issuer", "response": {"$ref": "IssuerListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the issuer referenced by the given issuer ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/issuer/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.issuer.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an issuer.", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/issuer/{resourceId}", "request": {"$ref": "Issuer"}, "response": {"$ref": "Issuer"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the issuer referenced by the given issuer ID.", "flatPath": "walletobjects/v1/issuer/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.issuer.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an issuer.", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/issuer/{resourceId}", "request": {"$ref": "Issuer"}, "response": {"$ref": "Issuer"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "jwt": {"methods": {"insert": {"description": "Inserts the resources in the JWT.", "flatPath": "walletobjects/v1/jwt", "httpMethod": "POST", "id": "walletobjects.jwt.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/jwt", "request": {"$ref": "JwtResource"}, "response": {"$ref": "JwtInsertResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "loyaltyclass": {"methods": {"addmessage": {"description": "Adds a message to the loyalty class referenced by the given class ID.", "flatPath": "walletobjects/v1/loyaltyClass/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.loyaltyclass.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/loyaltyClass/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "LoyaltyClassAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the loyalty class with the given class ID.", "flatPath": "walletobjects/v1/loyaltyClass/{resourceId}", "httpMethod": "GET", "id": "walletobjects.loyaltyclass.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/loyaltyClass/{resourceId}", "response": {"$ref": "LoyaltyClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts an loyalty class with the given ID and properties.", "flatPath": "walletobjects/v1/loyaltyClass", "httpMethod": "POST", "id": "walletobjects.loyaltyclass.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/loyaltyClass", "request": {"$ref": "LoyaltyClass"}, "response": {"$ref": "LoyaltyClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all loyalty classes for a given issuer ID.", "flatPath": "walletobjects/v1/loyaltyClass", "httpMethod": "GET", "id": "walletobjects.loyaltyclass.list", "parameterOrder": [], "parameters": {"issuerId": {"description": "The ID of the issuer authorized to list classes.", "format": "int64", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` classes are available in a list. For example, if you have a list of 200 classes and you call list with `maxResults` set to 20, list will return the first 20 classes and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 classes.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/loyaltyClass", "response": {"$ref": "LoyaltyClassListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the loyalty class referenced by the given class ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/loyaltyClass/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.loyaltyclass.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/loyaltyClass/{resourceId}", "request": {"$ref": "LoyaltyClass"}, "response": {"$ref": "LoyaltyClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the loyalty class referenced by the given class ID.", "flatPath": "walletobjects/v1/loyaltyClass/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.loyaltyclass.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/loyaltyClass/{resourceId}", "request": {"$ref": "LoyaltyClass"}, "response": {"$ref": "LoyaltyClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "loyaltyobject": {"methods": {"addmessage": {"description": "Adds a message to the loyalty object referenced by the given object ID.", "flatPath": "walletobjects/v1/loyaltyObject/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.loyaltyobject.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/loyaltyObject/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "LoyaltyObjectAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the loyalty object with the given object ID.", "flatPath": "walletobjects/v1/loyaltyObject/{resourceId}", "httpMethod": "GET", "id": "walletobjects.loyaltyobject.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/loyaltyObject/{resourceId}", "response": {"$ref": "LoyaltyObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts an loyalty object with the given ID and properties.", "flatPath": "walletobjects/v1/loyaltyObject", "httpMethod": "POST", "id": "walletobjects.loyaltyobject.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/loyaltyObject", "request": {"$ref": "LoyaltyObject"}, "response": {"$ref": "LoyaltyObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all loyalty objects for a given issuer ID.", "flatPath": "walletobjects/v1/loyaltyObject", "httpMethod": "GET", "id": "walletobjects.loyaltyobject.list", "parameterOrder": [], "parameters": {"classId": {"description": "The ID of the class whose objects will be listed.", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` objects are available in a list. For example, if you have a list of 200 objects and you call list with `maxResults` set to 20, list will return the first 20 objects and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 objects.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/loyaltyObject", "response": {"$ref": "LoyaltyObjectListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "modifylinkedofferobjects": {"description": "Modifies linked offer objects for the loyalty object with the given ID.", "flatPath": "walletobjects/v1/loyaltyObject/{resourceId}/modifyLinkedOfferObjects", "httpMethod": "POST", "id": "walletobjects.loyaltyobject.modifylinkedofferobjects", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/loyaltyObject/{resourceId}/modifyLinkedOfferObjects", "request": {"$ref": "ModifyLinkedOfferObjectsRequest"}, "response": {"$ref": "LoyaltyObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the loyalty object referenced by the given object ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/loyaltyObject/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.loyaltyobject.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/loyaltyObject/{resourceId}", "request": {"$ref": "LoyaltyObject"}, "response": {"$ref": "LoyaltyObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the loyalty object referenced by the given object ID.", "flatPath": "walletobjects/v1/loyaltyObject/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.loyaltyobject.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/loyaltyObject/{resourceId}", "request": {"$ref": "LoyaltyObject"}, "response": {"$ref": "LoyaltyObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "media": {"methods": {"download": {"description": "Downloads rotating barcode values for the transit object referenced by the given object ID.", "flatPath": "walletobjects/v1/transitObject/{resourceId}/downloadRotatingBarcodeValues", "httpMethod": "GET", "id": "walletobjects.media.download", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/transitObject/{resourceId}/downloadRotatingBarcodeValues", "response": {"$ref": "Media"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"], "supportsMediaDownload": true, "useMediaDownloadService": true}, "upload": {"description": "Uploads rotating barcode values for the transit object referenced by the given object ID. Note the max upload size is specified in google3/production/config/cdd/apps-upload/customers/payments-consumer-passes/config.gcl and enforced by <PERSON><PERSON>.", "flatPath": "walletobjects/v1/transitObject/{resourceId}/uploadRotatingBarcodeValues", "httpMethod": "POST", "id": "walletobjects.media.upload", "mediaUpload": {"accept": ["*/*"], "protocols": {"simple": {"multipart": true, "path": "/upload/walletobjects/v1/transitObject/{resourceId}/uploadRotatingBarcodeValues"}}}, "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/transitObject/{resourceId}/uploadRotatingBarcodeValues", "request": {"$ref": "TransitObjectUploadRotatingBarcodeValuesRequest"}, "response": {"$ref": "TransitObjectUploadRotatingBarcodeValuesResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"], "supportsMediaUpload": true}}}, "offerclass": {"methods": {"addmessage": {"description": "Adds a message to the offer class referenced by the given class ID.", "flatPath": "walletobjects/v1/offerClass/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.offerclass.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/offerClass/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "OfferClassAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the offer class with the given class ID.", "flatPath": "walletobjects/v1/offerClass/{resourceId}", "httpMethod": "GET", "id": "walletobjects.offerclass.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/offerClass/{resourceId}", "response": {"$ref": "OfferClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts an offer class with the given ID and properties.", "flatPath": "walletobjects/v1/offerClass", "httpMethod": "POST", "id": "walletobjects.offerclass.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/offerClass", "request": {"$ref": "OfferClass"}, "response": {"$ref": "OfferClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all offer classes for a given issuer ID.", "flatPath": "walletobjects/v1/offerClass", "httpMethod": "GET", "id": "walletobjects.offerclass.list", "parameterOrder": [], "parameters": {"issuerId": {"description": "The ID of the issuer authorized to list classes.", "format": "int64", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` classes are available in a list. For example, if you have a list of 200 classes and you call list with `maxResults` set to 20, list will return the first 20 classes and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 classes.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/offerClass", "response": {"$ref": "OfferClassListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the offer class referenced by the given class ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/offerClass/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.offerclass.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/offerClass/{resourceId}", "request": {"$ref": "OfferClass"}, "response": {"$ref": "OfferClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the offer class referenced by the given class ID.", "flatPath": "walletobjects/v1/offerClass/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.offerclass.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/offerClass/{resourceId}", "request": {"$ref": "OfferClass"}, "response": {"$ref": "OfferClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "offerobject": {"methods": {"addmessage": {"description": "Adds a message to the offer object referenced by the given object ID.", "flatPath": "walletobjects/v1/offerObject/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.offerobject.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/offerObject/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "OfferObjectAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the offer object with the given object ID.", "flatPath": "walletobjects/v1/offerObject/{resourceId}", "httpMethod": "GET", "id": "walletobjects.offerobject.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/offerObject/{resourceId}", "response": {"$ref": "OfferObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts an offer object with the given ID and properties.", "flatPath": "walletobjects/v1/offerObject", "httpMethod": "POST", "id": "walletobjects.offerobject.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/offerObject", "request": {"$ref": "OfferObject"}, "response": {"$ref": "OfferObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all offer objects for a given issuer ID.", "flatPath": "walletobjects/v1/offerObject", "httpMethod": "GET", "id": "walletobjects.offerobject.list", "parameterOrder": [], "parameters": {"classId": {"description": "The ID of the class whose objects will be listed.", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` objects are available in a list. For example, if you have a list of 200 objects and you call list with `maxResults` set to 20, list will return the first 20 objects and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 objects.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/offerObject", "response": {"$ref": "OfferObjectListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the offer object referenced by the given object ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/offerObject/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.offerobject.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/offerObject/{resourceId}", "request": {"$ref": "OfferObject"}, "response": {"$ref": "OfferObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the offer object referenced by the given object ID.", "flatPath": "walletobjects/v1/offerObject/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.offerobject.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/offerObject/{resourceId}", "request": {"$ref": "OfferObject"}, "response": {"$ref": "OfferObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "permissions": {"methods": {"get": {"description": "Returns the permissions for the given issuer id.", "flatPath": "walletobjects/v1/permissions/{resourceId}", "httpMethod": "GET", "id": "walletobjects.permissions.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an issuer. This ID must be unique across all issuers.", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/permissions/{resourceId}", "response": {"$ref": "Permissions"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the permissions for the given issuer.", "flatPath": "walletobjects/v1/permissions/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.permissions.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an issuer. This ID must be unique across all issuers.", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/permissions/{resourceId}", "request": {"$ref": "Permissions"}, "response": {"$ref": "Permissions"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "smarttap": {"methods": {"insert": {"description": "Inserts the smart tap.", "flatPath": "walletobjects/v1/smartTap", "httpMethod": "POST", "id": "walletobjects.smarttap.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/smartTap", "request": {"$ref": "SmartTap"}, "response": {"$ref": "SmartTap"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "transitclass": {"methods": {"addmessage": {"description": "Adds a message to the transit class referenced by the given class ID.", "flatPath": "walletobjects/v1/transitClass/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.transitclass.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/transitClass/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "TransitClassAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the transit class with the given class ID.", "flatPath": "walletobjects/v1/transitClass/{resourceId}", "httpMethod": "GET", "id": "walletobjects.transitclass.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/transitClass/{resourceId}", "response": {"$ref": "TransitClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts a transit class with the given ID and properties.", "flatPath": "walletobjects/v1/transitClass", "httpMethod": "POST", "id": "walletobjects.transitclass.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/transitClass", "request": {"$ref": "TransitClass"}, "response": {"$ref": "TransitClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all transit classes for a given issuer ID.", "flatPath": "walletobjects/v1/transitClass", "httpMethod": "GET", "id": "walletobjects.transitclass.list", "parameterOrder": [], "parameters": {"issuerId": {"description": "The ID of the issuer authorized to list classes.", "format": "int64", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` classes are available in a list. For example, if you have a list of 200 classes and you call list with `maxResults` set to 20, list will return the first 20 classes and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 classes.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/transitClass", "response": {"$ref": "TransitClassListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the transit class referenced by the given class ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/transitClass/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.transitclass.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/transitClass/{resourceId}", "request": {"$ref": "TransitClass"}, "response": {"$ref": "TransitClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the transit class referenced by the given class ID.", "flatPath": "walletobjects/v1/transitClass/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.transitclass.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/transitClass/{resourceId}", "request": {"$ref": "TransitClass"}, "response": {"$ref": "TransitClass"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "transitobject": {"methods": {"addmessage": {"description": "Adds a message to the transit object referenced by the given object ID.", "flatPath": "walletobjects/v1/transitObject/{resourceId}/addMessage", "httpMethod": "POST", "id": "walletobjects.transitobject.addmessage", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/transitObject/{resourceId}/addMessage", "request": {"$ref": "AddMessageRequest"}, "response": {"$ref": "TransitObjectAddMessageResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "get": {"description": "Returns the transit object with the given object ID.", "flatPath": "walletobjects/v1/transitObject/{resourceId}", "httpMethod": "GET", "id": "walletobjects.transitobject.get", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/transitObject/{resourceId}", "response": {"$ref": "TransitObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "insert": {"description": "Inserts an transit object with the given ID and properties.", "flatPath": "walletobjects/v1/transitObject", "httpMethod": "POST", "id": "walletobjects.transitobject.insert", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/transitObject", "request": {"$ref": "TransitObject"}, "response": {"$ref": "TransitObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "list": {"description": "Returns a list of all transit objects for a given issuer ID.", "flatPath": "walletobjects/v1/transitObject", "httpMethod": "GET", "id": "walletobjects.transitobject.list", "parameterOrder": [], "parameters": {"classId": {"description": "The ID of the class whose objects will be listed.", "location": "query", "type": "string"}, "maxResults": {"description": "Identifies the max number of results returned by a list. All results are returned if `maxResults` isn't defined.", "format": "int32", "location": "query", "type": "integer"}, "token": {"description": "Used to get the next set of results if `maxResults` is specified, but more than `maxResults` objects are available in a list. For example, if you have a list of 200 objects and you call list with `maxResults` set to 20, list will return the first 20 objects and a token. Call list again with `maxResults` set to 20 and the token to get the next 20 objects.", "location": "query", "type": "string"}}, "path": "walletobjects/v1/transitObject", "response": {"$ref": "TransitObjectListResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "patch": {"description": "Updates the transit object referenced by the given object ID. This method supports patch semantics.", "flatPath": "walletobjects/v1/transitObject/{resourceId}", "httpMethod": "PATCH", "id": "walletobjects.transitobject.patch", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/transitObject/{resourceId}", "request": {"$ref": "TransitObject"}, "response": {"$ref": "TransitObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}, "update": {"description": "Updates the transit object referenced by the given object ID.", "flatPath": "walletobjects/v1/transitObject/{resourceId}", "httpMethod": "PUT", "id": "walletobjects.transitobject.update", "parameterOrder": ["resourceId"], "parameters": {"resourceId": {"description": "The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "location": "path", "required": true, "type": "string"}}, "path": "walletobjects/v1/transitObject/{resourceId}", "request": {"$ref": "TransitObject"}, "response": {"$ref": "TransitObject"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}, "walletobjects": {"resources": {"v1": {"resources": {"privateContent": {"methods": {"setPassUpdateNotice": {"description": "Provide Google with information about awaiting private pass update. This will allow Google to provide the update notification to the device that currently holds this pass.", "flatPath": "walletobjects/v1/privateContent/setPassUpdateNotice", "httpMethod": "POST", "id": "walletobjects.walletobjects.v1.privateContent.setPassUpdateNotice", "parameterOrder": [], "parameters": {}, "path": "walletobjects/v1/privateContent/setPassUpdateNotice", "request": {"$ref": "SetPassUpdateNoticeRequest"}, "response": {"$ref": "SetPassUpdateNoticeResponse"}, "scopes": ["https://www.googleapis.com/auth/wallet_object.issuer"]}}}}}}}}, "revision": "20250506", "rootUrl": "https://walletobjects.googleapis.com/", "schemas": {"ActivationOptions": {"description": "ActivationOptions for the class", "id": "ActivationOptions", "properties": {"activationUrl": {"description": "HTTPS URL that supports REST semantics. Would be used for requesting activation from partners for given valuable, triggered by the users.", "type": "string"}, "allowReactivation": {"description": "Flag to allow users to make activation call from different device. This allows client to render the activation button enabled even if the activationStatus is ACTIVATED but the requested device is different than the current device.", "type": "boolean"}}, "type": "object"}, "ActivationStatus": {"description": "The activation status of the object. This field includes activation status if valuable supports activation.", "id": "ActivationStatus", "properties": {"state": {"enum": ["UNKNOWN_STATE", "NOT_ACTIVATED", "not_activated", "ACTIVATED", "activated"], "enumDeprecated": [false, false, true, false, true], "enumDescriptions": ["", "Not-Activated, this is the default status", "Legacy alias for `NOT_ACTIVATED`. Deprecated.", "Activated", "Legacy alias for `ACTIVATED`. Deprecated."], "type": "string"}}, "type": "object"}, "AddMessageRequest": {"description": "Resource used when the AddMessage endpoints are called.", "id": "AddMessageRequest", "properties": {"message": {"$ref": "Message"}}, "type": "object"}, "AirportInfo": {"id": "AirportInfo", "properties": {"airportIataCode": {"description": "Three character IATA airport code. This is a required field for `origin` and `destination`. Eg: \"SFO\"", "type": "string"}, "airportNameOverride": {"$ref": "LocalizedString", "description": "Optional field that overrides the airport city name defined by IATA. By default, Google takes the `airportIataCode` provided and maps it to the official airport city name defined by IATA. Official IATA airport city names can be found at IATA airport city names website. For example, for the airport IATA code \"LTN\", IATA website tells us that the corresponding airport city is \"London\". If this field is not populated, Google would display \"London\". However, populating this field with a custom name (eg: \"London Luton\") would override it."}, "gate": {"description": "A name of the gate. Eg: \"B59\" or \"59\"", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#airportInfo\"`.", "type": "string"}, "terminal": {"description": "Terminal name. Eg: \"IN<PERSON>\" or \"I\"", "type": "string"}}, "type": "object"}, "AppLinkData": {"id": "AppLinkData", "properties": {"androidAppLinkInfo": {"$ref": "AppLinkDataAppLinkInfo", "description": "Optional information about the partner app link."}, "displayText": {"$ref": "LocalizedString", "description": "Optional display text for the app link button. Character limit is 30."}, "iosAppLinkInfo": {"$ref": "AppLinkDataAppLinkInfo", "deprecated": true, "description": "Deprecated. Links to open iOS apps are not supported."}, "webAppLinkInfo": {"$ref": "AppLinkDataAppLinkInfo", "description": "Optional information about the partner web link."}}, "type": "object"}, "AppLinkDataAppLinkInfo": {"id": "AppLinkDataAppLinkInfo", "properties": {"appLogoImage": {"$ref": "Image", "deprecated": true, "description": "Deprecated. Image isn't supported in the app link module."}, "appTarget": {"$ref": "AppLinkDataAppLinkInfoAppTarget", "description": "Target to follow when opening the app link on clients. It will be used by partners to open their app or webpage."}, "description": {"$ref": "LocalizedString", "deprecated": true, "description": "Deprecated. Description isn't supported in the app link module."}, "title": {"$ref": "LocalizedString", "deprecated": true, "description": "Deprecated. Title isn't supported in the app link module."}}, "type": "object"}, "AppLinkDataAppLinkInfoAppTarget": {"id": "AppLinkDataAppLinkInfoAppTarget", "properties": {"packageName": {"description": "Package name for AppTarget. For example: com.google.android.gm", "type": "string"}, "targetUri": {"$ref": "<PERSON><PERSON>", "description": "URI for AppTarget. The description on the URI must be set. Prefer setting package field instead, if this target is defined for your application."}}, "type": "object"}, "AuthenticationKey": {"id": "AuthenticationKey", "properties": {"id": {"description": "Available only to Smart Tap enabled partners. Contact support for additional guidance.", "format": "int32", "type": "integer"}, "publicKeyPem": {"description": "Available only to Smart Tap enabled partners. Contact support for additional guidance.", "type": "string"}}, "type": "object"}, "Barcode": {"id": "Barcode", "properties": {"alternateText": {"description": "An optional text that will override the default text that shows under the barcode. This field is intended for a human readable equivalent of the barcode value, used when the barcode cannot be scanned.", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#barcode\"`.", "type": "string"}, "renderEncoding": {"description": "The render encoding for the barcode. When specified, barcode is rendered in the given encoding. Otherwise best known encoding is chosen by Google.", "enum": ["RENDER_ENCODING_UNSPECIFIED", "UTF_8"], "enumDescriptions": ["", "UTF_8 encoding for barcodes. This is only supported for barcode type qrCode."], "type": "string"}, "showCodeText": {"$ref": "LocalizedString", "description": "Optional text that will be shown when the barcode is hidden behind a click action. This happens in cases where a pass has Smart Tap enabled. If not specified, a default is chosen by Google."}, "type": {"description": "The type of barcode.", "enum": ["BARCODE_TYPE_UNSPECIFIED", "AZTEC", "aztec", "CODE_39", "code39", "CODE_128", "code128", "CODABAR", "codabar", "DATA_MATRIX", "dataMatrix", "EAN_8", "ean8", "EAN_13", "ean13", "EAN13", "ITF_14", "itf14", "PDF_417", "pdf417", "PDF417", "QR_CODE", "qrCode", "qrcode", "UPC_A", "upcA", "TEXT_ONLY", "textOnly"], "enumDeprecated": [false, false, true, false, true, false, true, false, true, false, true, false, true, false, true, true, false, true, false, true, true, false, true, true, false, true, false, true], "enumDescriptions": ["", "Not supported for Rotating Barcodes.", "Legacy alias for `AZTEC`. Deprecated. Not supported for Rotating Barcodes.", "Not supported for Rotating Barcodes.", "Legacy alias for `CODE_39`. Deprecated. Not supported for Rotating Barcodes.", "Not supported for Rotating Barcodes.", "Legacy alias for `CODE_128`. Deprecated. Not supported for Rotating Barcodes.", "Not supported for Rotating Barcodes.", "Legacy alias for `CODABAR`. Deprecated. Not supported for Rotating Barcodes.", "A 2D matrix barcode consisting of black and white. Cells or modules are arranged in either a square or rectangle. Not supported for Rotating Barcodes.", "Legacy alias for `DATA_MATRIX`. Deprecated. Not supported for Rotating Barcodes.", "Not supported for Rotating Barcodes.", "Legacy alias for `EAN_8`. Deprecated. Not supported for Rotating Barcodes.", "Not supported for Rotating Barcodes.", "Legacy alias for `EAN_13`. Deprecated. Not supported for Rotating Barcodes.", "Legacy alias for `EAN_13`. Deprecated. Not supported for Rotating Barcodes.", "14 digit ITF code Not supported for Rotating Barcodes.", "Legacy alias for `ITF_14`. Deprecated. Not supported for Rotating Barcodes.", "Supported for Rotating Barcodes.", "Legacy alias for `PDF_417`. Deprecated.", "Legacy alias for `PDF_417`. Deprecated.", "Supported for Rotating Barcodes.", "Legacy alias for `QR_CODE`. Deprecated.", "Legacy alias for `QR_CODE`. Deprecated.", "11 or 12 digit codes Not supported for Rotating Barcodes.", "Legacy alias for `UPC_A`. Deprecated. Not supported for Rotating Barcodes.", "Renders the field as a text field. The `alternateText` field may not be used with a barcode of type `textOnly`. Not supported for Rotating Barcodes.", "Legacy alias for `TEXT_ONLY`. Deprecated. Not supported for Rotating Barcodes."], "type": "string"}, "value": {"description": "The value encoded in the barcode.", "type": "string"}}, "type": "object"}, "BarcodeSectionDetail": {"id": "BarcodeSectionDetail", "properties": {"fieldSelector": {"$ref": "FieldSelector", "description": "A reference to an existing text-based or image field to display."}}, "type": "object"}, "Blobstore2Info": {"description": "Information to read/write to blobstore2.", "id": "Blobstore2Info", "properties": {"blobGeneration": {"description": "The blob generation id.", "format": "int64", "type": "string"}, "blobId": {"description": "The blob id, e.g., /blobstore/prod/playground/scotty", "type": "string"}, "downloadReadHandle": {"description": "Read handle passed from Bigstore -> <PERSON><PERSON> for a GCS download. This is a signed, serialized blobstore2.ReadHandle proto which must never be set outside of Bigstore, and is not applicable to non-GCS media downloads.", "format": "byte", "type": "string"}, "readToken": {"description": "The blob read token. Needed to read blobs that have not been replicated. Might not be available until the final call.", "type": "string"}, "uploadMetadataContainer": {"description": "Metadata passed from Blobstore -> <PERSON><PERSON> for a new GCS upload. This is a signed, serialized blobstore2.BlobMetadataContainer proto which must never be consumed outside of Bigstore, and is not applicable to non-GCS media uploads.", "format": "byte", "type": "string"}}, "type": "object"}, "BoardingAndSeatingInfo": {"id": "BoardingAndSeatingInfo", "properties": {"boardingDoor": {"description": "Set this field only if this flight boards through more than one door or bridge and you want to explicitly print the door location on the boarding pass. Most airlines route their passengers to the right door or bridge by refering to doors/bridges by the `seatClass`. In those cases `boardingDoor` should not be set.", "enum": ["BOARDING_DOOR_UNSPECIFIED", "FRONT", "front", "BACK", "back"], "enumDeprecated": [false, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `FRONT`. Deprecated.", "", "Legacy alias for `<PERSON>C<PERSON>`. Deprecated."], "type": "string"}, "boardingGroup": {"description": "The value of boarding group (or zone) this passenger shall board with. eg: \"B\" The label for this value will be determined by the `boardingPolicy` field in the `flightClass` referenced by this object.", "type": "string"}, "boardingPosition": {"description": "The value of boarding position. eg: \"76\"", "type": "string"}, "boardingPrivilegeImage": {"$ref": "Image", "description": "A small image shown above the boarding barcode. Airlines can use it to communicate any special boarding privileges. In the event the security program logo is also set, this image might be rendered alongside the logo for that security program."}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#boardingAndSeatingInfo\"`.", "type": "string"}, "seatAssignment": {"$ref": "LocalizedString", "description": "The passenger's seat assignment. To be used when there is no specific identifier to use in `seatNumber`. eg: \"assigned at gate\""}, "seatClass": {"description": "The value of the seat class. eg: \"Economy\" or \"Economy Plus\"", "type": "string"}, "seatNumber": {"description": "The value of passenger seat. If there is no specific identifier, use `seatAssignment` instead. eg: \"25A\"", "type": "string"}, "sequenceNumber": {"description": "The sequence number on the boarding pass. This usually matches the sequence in which the passengers checked in. Airline might use the number for manual boarding and bag tags. eg: \"49\"", "type": "string"}}, "type": "object"}, "BoardingAndSeatingPolicy": {"id": "BoardingAndSeatingPolicy", "properties": {"boardingPolicy": {"description": "Indicates the policy the airline uses for boarding. If unset, Google will default to `zoneBased`.", "enum": ["BOARDING_POLICY_UNSPECIFIED", "ZONE_BASED", "zoneBased", "GROUP_BASED", "groupBased", "BOARDING_POLICY_OTHER", "boardingPolicyOther"], "enumDeprecated": [false, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `<PERSON><PERSON><PERSON>_BASED`. Deprecated.", "", "Legacy alias for `GROUP_BASED`. Deprecated.", "", "Legacy alias for `BOARDING_POLICY_OTHER`. Deprecated."], "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#boardingAndSeatingPolicy\"`.", "type": "string"}, "seatClassPolicy": {"description": "Seating policy which dictates how we display the seat class. If unset, Google will default to `cabinBased`.", "enum": ["SEAT_CLASS_POLICY_UNSPECIFIED", "CABIN_BASED", "cabinBased", "CLASS_BASED", "classBased", "TIER_BASED", "tierBased", "SEAT_CLASS_POLICY_OTHER", "seatClassPolicyOther"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `CABIN_BASED`. Deprecated.", "", "Legacy alias for `CLASS_BASED`. Deprecated.", "", "Legacy alias for `TIER_BASED`. Deprecated.", "", "Legacy alias for `SEAT_CLASS_POLICY_OTHER`. Deprecated."], "type": "string"}}, "type": "object"}, "CallbackOptions": {"id": "CallbackOptions", "properties": {"updateRequestUrl": {"deprecated": true, "description": "URL for the merchant endpoint that would be called to request updates. The URL should be hosted on HTTPS and robots.txt should allow the URL path to be accessible by UserAgent:Googlebot. Deprecated.", "type": "string"}, "url": {"description": "The HTTPS url configured by the merchant. The URL should be hosted on HTTPS and robots.txt should allow the URL path to be accessible by UserAgent:Googlebot.", "type": "string"}}, "type": "object"}, "CardBarcodeSectionDetails": {"id": "CardBarcodeSectionDetails", "properties": {"firstBottomDetail": {"$ref": "BarcodeSectionDetail", "description": "Optional information to display below the barcode."}, "firstTopDetail": {"$ref": "BarcodeSectionDetail", "description": "Optional information to display above the barcode. If `secondTopDetail` is defined, this will be displayed to the start side of this detail section."}, "secondTopDetail": {"$ref": "BarcodeSectionDetail", "description": "Optional second piece of information to display above the barcode. If `firstTopDetail` is defined, this will be displayed to the end side of this detail section."}}, "type": "object"}, "CardRowOneItem": {"id": "CardRowOneItem", "properties": {"item": {"$ref": "TemplateItem", "description": "The item to be displayed in the row. This item will be automatically centered."}}, "type": "object"}, "CardRowTemplateInfo": {"id": "CardRowTemplateInfo", "properties": {"oneItem": {"$ref": "CardRowOneItem", "description": "Template for a row containing one item. Exactly one of \"one_item\", \"two_items\", \"three_items\" must be set."}, "threeItems": {"$ref": "CardRowThreeItems", "description": "Template for a row containing three items. Exactly one of \"one_item\", \"two_items\", \"three_items\" must be set."}, "twoItems": {"$ref": "CardRowTwoItems", "description": "Template for a row containing two items. Exactly one of \"one_item\", \"two_items\", \"three_items\" must be set."}}, "type": "object"}, "CardRowThreeItems": {"id": "CardRowThreeItems", "properties": {"endItem": {"$ref": "TemplateItem", "description": "The item to be displayed at the end of the row. This item will be aligned to the right."}, "middleItem": {"$ref": "TemplateItem", "description": "The item to be displayed in the middle of the row. This item will be centered between the start and end items."}, "startItem": {"$ref": "TemplateItem", "description": "The item to be displayed at the start of the row. This item will be aligned to the left."}}, "type": "object"}, "CardRowTwoItems": {"id": "CardRowTwoItems", "properties": {"endItem": {"$ref": "TemplateItem", "description": "The item to be displayed at the end of the row. This item will be aligned to the right."}, "startItem": {"$ref": "TemplateItem", "description": "The item to be displayed at the start of the row. This item will be aligned to the left."}}, "type": "object"}, "CardTemplateOverride": {"id": "CardTemplateOverride", "properties": {"cardRowTemplateInfos": {"description": "Template information for rows in the card view. At most three rows are allowed to be specified.", "items": {"$ref": "CardRowTemplateInfo"}, "type": "array"}}, "type": "object"}, "ClassTemplateInfo": {"id": "ClassTemplateInfo", "properties": {"cardBarcodeSectionDetails": {"$ref": "CardBarcodeSectionDetails", "description": "Specifies extra information to be displayed above and below the barcode."}, "cardTemplateOverride": {"$ref": "CardTemplateOverride", "description": "Override for the card view."}, "detailsTemplateOverride": {"$ref": "DetailsTemplateOverride", "description": "Override for the details view (beneath the card view)."}, "listTemplateOverride": {"$ref": "ListTemplateOverride", "description": "Override for the passes list view."}}, "type": "object"}, "CompositeMedia": {"description": "A sequence of media data references representing composite data. Introduced to support Bigstore composite objects. For details, visit http://go/bigstore-composites.", "id": "CompositeMedia", "properties": {"blobRef": {"deprecated": true, "description": "Blobstore v1 reference, set if reference_type is BLOBSTORE_REF This should be the byte representation of a blobstore.BlobRef. Since Blobstore is deprecating v1, use blobstore2_info instead. For now, any v2 blob will also be represented in this field as v1 BlobRef.", "format": "byte", "type": "string"}, "blobstore2Info": {"$ref": "Blobstore2Info", "description": "Blobstore v2 info, set if reference_type is BLOBSTORE_REF and it refers to a v2 blob."}, "cosmoBinaryReference": {"description": "A binary data reference for a media download. Serves as a technology-agnostic binary reference in some Google infrastructure. This value is a serialized storage_cosmo.BinaryReference proto. Storing it as bytes is a hack to get around the fact that the cosmo proto (as well as others it includes) doesn't support JavaScript. This prevents us from including the actual type of this field.", "format": "byte", "type": "string"}, "crc32cHash": {"description": "crc32.c hash for the payload.", "format": "uint32", "type": "integer"}, "inline": {"description": "Media data, set if reference_type is INLINE", "format": "byte", "type": "string"}, "length": {"description": "Size of the data, in bytes", "format": "int64", "type": "string"}, "md5Hash": {"description": "MD5 hash for the payload.", "format": "byte", "type": "string"}, "objectId": {"$ref": "ObjectId", "description": "Reference to a TI Blob, set if reference_type is BIGSTORE_REF."}, "path": {"description": "Path to the data, set if reference_type is PATH", "type": "string"}, "referenceType": {"description": "Describes what the field reference contains.", "enum": ["PATH", "BLOB_REF", "INLINE", "BIGSTORE_REF", "COSMO_BINARY_REFERENCE"], "enumDescriptions": ["Reference contains a GFS path or a local path.", "Reference points to a blobstore object. This could be either a v1 blob_ref or a v2 blobstore2_info. Clients should check blobstore2_info first, since v1 is being deprecated.", "Data is included into this proto buffer", "Reference points to a bigstore object", "Indicates the data is stored in cosmo_binary_reference."], "type": "string"}, "sha1Hash": {"description": "SHA-1 hash for the payload.", "format": "byte", "type": "string"}}, "type": "object"}, "ContentTypeInfo": {"description": "Detailed Content-Type information from <PERSON><PERSON>. The Content-Type of the media will typically be filled in by the header or <PERSON><PERSON>'s best_guess, but this extended information provides the backend with more information so that it can make a better decision if needed. This is only used on media upload requests from <PERSON><PERSON>.", "id": "ContentTypeInfo", "properties": {"bestGuess": {"description": "<PERSON><PERSON>'s best guess of what the content type of the file is.", "type": "string"}, "fromBytes": {"description": "The content type of the file derived by looking at specific bytes (i.e. \"magic bytes\") of the actual file.", "type": "string"}, "fromFileName": {"description": "The content type of the file derived from the file extension of the original file name used by the client.", "type": "string"}, "fromHeader": {"description": "The content type of the file as specified in the request headers, multipart headers, or RUPIO start request.", "type": "string"}, "fromUrlPath": {"description": "The content type of the file derived from the file extension of the URL path. The URL path is assumed to represent a file name (which is typically only true for agents that are providing a REST API).", "type": "string"}}, "type": "object"}, "DateTime": {"id": "DateTime", "properties": {"date": {"description": "An ISO 8601 extended format date/time. Offset may or may not be required (refer to the parent field's documentation). Time may be specified up to nanosecond precision. Offsets may be specified with seconds precision (even though offset seconds is not part of ISO 8601). For example: `1985-04-12T23:20:50.52Z` would be 20 minutes and 50.52 seconds after the 23rd hour of April 12th, 1985 in UTC. `1985-04-12T19:20:50.52-04:00` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985, 4 hours before UTC (same instant in time as the above example). If the date/time is intended for a physical location in New York, this would be the equivalent of Eastern Daylight Time (EDT). Remember that offset varies in regions that observe Daylight Saving Time (or Summer Time), depending on the time of the year. `1985-04-12T19:20:50.52` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985 with no offset information. Providing an offset makes this an absolute instant in time around the world. The date/time will be adjusted based on the user's time zone. For example, a time of `2018-06-19T18:30:00-04:00` will be 18:30:00 for a user in New York and 15:30:00 for a user in Los Angeles. Omitting the offset makes this a local date/time, representing several instants in time around the world. The date/time will always be in the user's current time zone. For example, a time of `2018-06-19T18:30:00` will be 18:30:00 for a user in New York and also 18:30:00 for a user in Los Angeles. This is useful when the same local date/time should apply to many physical locations across several time zones.", "type": "string"}}, "type": "object"}, "DetailsItemInfo": {"id": "DetailsItemInfo", "properties": {"item": {"$ref": "TemplateItem", "description": "The item to be displayed in the details list."}}, "type": "object"}, "DetailsTemplateOverride": {"id": "DetailsTemplateOverride", "properties": {"detailsItemInfos": {"description": "Information for the \"nth\" item displayed in the details list.", "items": {"$ref": "DetailsItemInfo"}, "type": "array"}}, "type": "object"}, "DeviceContext": {"description": "Device context associated with the object.", "id": "DeviceContext", "properties": {"deviceToken": {"description": "If set, redemption information will only be returned to the given device upon activation of the object. This should not be used as a stable identifier to trace a user's device. It can change across different passes for the same device or even across different activations for the same device. When setting this, callers must also set has_linked_device on the object being activated.", "type": "string"}}, "type": "object"}, "DiffChecksumsResponse": {"description": "Backend response for a Diff get checksums response. For details on the Scotty Diff protocol, visit http://go/scotty-diff-protocol.", "id": "DiffChecksumsResponse", "properties": {"checksumsLocation": {"$ref": "CompositeMedia", "description": "Exactly one of these fields must be populated. If checksums_location is filled, the server will return the corresponding contents to the user. If object_location is filled, the server will calculate the checksums based on the content there and return that to the user. For details on the format of the checksums, see http://go/scotty-diff-protocol."}, "chunkSizeBytes": {"description": "The chunk size of checksums. Must be a multiple of 256KB.", "format": "int64", "type": "string"}, "objectLocation": {"$ref": "CompositeMedia", "description": "If set, calculate the checksums based on the contents and return them to the caller."}, "objectSizeBytes": {"description": "The total size of the server object.", "format": "int64", "type": "string"}, "objectVersion": {"description": "The object version of the object the checksums are being returned for.", "type": "string"}}, "type": "object"}, "DiffDownloadResponse": {"description": "Backend response for a Diff download response. For details on the Scotty Diff protocol, visit http://go/scotty-diff-protocol.", "id": "DiffDownloadResponse", "properties": {"objectLocation": {"$ref": "CompositeMedia", "description": "The original object location."}}, "type": "object"}, "DiffUploadRequest": {"description": "A Diff upload request. For details on the Scotty Diff protocol, visit http://go/scotty-diff-protocol.", "id": "DiffUploadRequest", "properties": {"checksumsInfo": {"$ref": "CompositeMedia", "description": "The location of the checksums for the new object. Agents must clone the object located here, as the upload server will delete the contents once a response is received. For details on the format of the checksums, see http://go/scotty-diff-protocol."}, "objectInfo": {"$ref": "CompositeMedia", "description": "The location of the new object. Agents must clone the object located here, as the upload server will delete the contents once a response is received."}, "objectVersion": {"description": "The object version of the object that is the base version the incoming diff script will be applied to. This field will always be filled in.", "type": "string"}}, "type": "object"}, "DiffUploadResponse": {"description": "Backend response for a Diff upload request. For details on the Scotty Diff protocol, visit http://go/scotty-diff-protocol.", "id": "DiffUploadResponse", "properties": {"objectVersion": {"description": "The object version of the object at the server. Must be included in the end notification response. The version in the end notification response must correspond to the new version of the object that is now stored at the server, after the upload.", "type": "string"}, "originalObject": {"$ref": "CompositeMedia", "description": "The location of the original file for a diff upload request. Must be filled in if responding to an upload start notification."}}, "type": "object"}, "DiffVersionResponse": {"description": "Backend response for a Diff get version response. For details on the Scotty Diff protocol, visit http://go/scotty-diff-protocol.", "id": "DiffVersionResponse", "properties": {"objectSizeBytes": {"description": "The total size of the server object.", "format": "int64", "type": "string"}, "objectVersion": {"description": "The version of the object stored at the server.", "type": "string"}}, "type": "object"}, "DiscoverableProgram": {"description": "Information about how a class may be discovered and instantiated from within the Google Wallet app. This is done by searching for a loyalty or gift card program and scanning or manually entering.", "id": "DiscoverableProgram", "properties": {"merchantSigninInfo": {"$ref": "DiscoverableProgramMerchantSigninInfo", "description": "Information about the ability to signin and add a valuable for this program through a merchant site. Used when MERCHANT_HOSTED_SIGNIN is enabled."}, "merchantSignupInfo": {"$ref": "DiscoverableProgramMerchantSignupInfo", "description": "Information about the ability to signup and add a valuable for this program through a merchant site. Used when MERCHANT_HOSTED_SIGNUP is enabled."}, "state": {"description": "Visibility state of the discoverable program.", "enum": ["STATE_UNSPECIFIED", "TRUSTED_TESTERS", "trustedTesters", "LIVE", "live", "DISABLED", "disabled"], "enumDeprecated": [false, false, true, false, true, false, true], "enumDescriptions": ["", "Visible only to testers that have access to issuer account.", "Legacy alias for `TRUSTED_TESTERS`. Deprecated.", "Visible to all.", "Legacy alias for `LIVE`. Deprecated.", "Not visible.", "Legacy alias for `DISABLED`. Deprecated."], "type": "string"}}, "type": "object"}, "DiscoverableProgramMerchantSigninInfo": {"description": "Information about the merchant hosted signin flow for a program.", "id": "DiscoverableProgramMerchantSigninInfo", "properties": {"signinWebsite": {"$ref": "<PERSON><PERSON>", "description": "The URL to direct the user to for the merchant's signin site."}}, "type": "object"}, "DiscoverableProgramMerchantSignupInfo": {"description": "Information about the merchant hosted signup flow for a program.", "id": "DiscoverableProgramMerchantSignupInfo", "properties": {"signupSharedDatas": {"description": "User data that is sent in a POST request to the signup website URL. This information is encoded and then shared so that the merchant's website can prefill fields used to enroll the user for the discoverable program.", "items": {"enum": ["SHARED_DATA_TYPE_UNSPECIFIED", "FIRST_NAME", "LAST_NAME", "STREET_ADDRESS", "ADDRESS_LINE_1", "ADDRESS_LINE_2", "ADDRESS_LINE_3", "CITY", "STATE", "ZIPCODE", "COUNTRY", "EMAIL", "PHONE"], "enumDescriptions": ["", "", "", "single line address field", "multi line address fields", "", "", "", "", "", "", "", ""], "type": "string"}, "type": "array"}, "signupWebsite": {"$ref": "<PERSON><PERSON>", "description": "The URL to direct the user to for the merchant's signup site."}}, "type": "object"}, "DownloadParameters": {"description": "Parameters specific to media downloads.", "id": "DownloadParameters", "properties": {"allowGzipCompression": {"description": "A boolean to be returned in the response to <PERSON><PERSON>. Allows/disallows gzip encoding of the payload content when the server thinks it's advantageous (hence, does not guarantee compression) which allows <PERSON><PERSON> to GZip the response to the client.", "type": "boolean"}, "ignoreRange": {"description": "Determining whether or not Apiary should skip the inclusion of any Content-Range header on its response to <PERSON><PERSON>.", "type": "boolean"}}, "type": "object"}, "EventDateTime": {"id": "EventDateTime", "properties": {"customDoorsOpenLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the doors open value (`doorsOpen`) on the card detail view. This should only be used if the default \"Doors Open\" label or one of the `doorsOpenLabel` options is not sufficient. Both `doorsOpenLabel` and `customDoorsOpenLabel` may not be set. If neither is set, the label will default to \"Doors Open\", localized. If the doors open field is unset, this label will not be used."}, "doorsOpen": {"description": "The date/time when the doors open at the venue. This is an ISO 8601 extended format date/time, with or without an offset. Time may be specified up to nanosecond precision. Offsets may be specified with seconds precision (even though offset seconds is not part of ISO 8601). For example: `1985-04-12T23:20:50.52Z` would be 20 minutes and 50.52 seconds after the 23rd hour of April 12th, 1985 in UTC. `1985-04-12T19:20:50.52-04:00` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985, 4 hours before UTC (same instant in time as the above example). If the event were in New York, this would be the equivalent of Eastern Daylight Time (EDT). Remember that offset varies in regions that observe Daylight Saving Time (or Summer Time), depending on the time of the year. `1985-04-12T19:20:50.52` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985 with no offset information. The portion of the date/time without the offset is considered the \"local date/time\". This should be the local date/time at the venue. For example, if the event occurs at the 20th hour of June 5th, 2018 at the venue, the local date/time portion should be `2018-06-05T20:00:00`. If the local date/time at the venue is 4 hours before UTC, an offset of `-04:00` may be appended. Without offset information, some rich features may not be available.", "type": "string"}, "doorsOpenLabel": {"description": "The label to use for the doors open value (`doorsOpen`) on the card detail view. Each available option maps to a set of localized strings, so that translations are shown to the user based on their locale. Both `doorsOpenLabel` and `customDoorsOpenLabel` may not be set. If neither is set, the label will default to \"Doors Open\", localized. If the doors open field is unset, this label will not be used.", "enum": ["DOORS_OPEN_LABEL_UNSPECIFIED", "DOORS_OPEN", "doorsOpen", "GATES_OPEN", "gatesOpen"], "enumDeprecated": [false, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `DOORS_OPEN`. Deprecated.", "", "Legacy alias for `GATES_OPEN`. Deprecated."], "type": "string"}, "end": {"description": "The date/time when the event ends. If the event spans multiple days, it should be the end date/time on the last day. This is an ISO 8601 extended format date/time, with or without an offset. Time may be specified up to nanosecond precision. Offsets may be specified with seconds precision (even though offset seconds is not part of ISO 8601). For example: `1985-04-12T23:20:50.52Z` would be 20 minutes and 50.52 seconds after the 23rd hour of April 12th, 1985 in UTC. `1985-04-12T19:20:50.52-04:00` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985, 4 hours before UTC (same instant in time as the above example). If the event were in New York, this would be the equivalent of Eastern Daylight Time (EDT). Remember that offset varies in regions that observe Daylight Saving Time (or Summer Time), depending on the time of the year. `1985-04-12T19:20:50.52` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985 with no offset information. The portion of the date/time without the offset is considered the \"local date/time\". This should be the local date/time at the venue. For example, if the event occurs at the 20th hour of June 5th, 2018 at the venue, the local date/time portion should be `2018-06-05T20:00:00`. If the local date/time at the venue is 4 hours before UTC, an offset of `-04:00` may be appended. Without offset information, some rich features may not be available.", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#eventDateTime\"`.", "type": "string"}, "start": {"description": "The date/time when the event starts. If the event spans multiple days, it should be the start date/time on the first day. This is an ISO 8601 extended format date/time, with or without an offset. Time may be specified up to nanosecond precision. Offsets may be specified with seconds precision (even though offset seconds is not part of ISO 8601). For example: `1985-04-12T23:20:50.52Z` would be 20 minutes and 50.52 seconds after the 23rd hour of April 12th, 1985 in UTC. `1985-04-12T19:20:50.52-04:00` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985, 4 hours before UTC (same instant in time as the above example). If the event were in New York, this would be the equivalent of Eastern Daylight Time (EDT). Remember that offset varies in regions that observe Daylight Saving Time (or Summer Time), depending on the time of the year. `1985-04-12T19:20:50.52` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985 with no offset information. The portion of the date/time without the offset is considered the \"local date/time\". This should be the local date/time at the venue. For example, if the event occurs at the 20th hour of June 5th, 2018 at the venue, the local date/time portion should be `2018-06-05T20:00:00`. If the local date/time at the venue is 4 hours before UTC, an offset of `-04:00` may be appended. Without offset information, some rich features may not be available.", "type": "string"}}, "type": "object"}, "EventReservationInfo": {"id": "EventReservationInfo", "properties": {"confirmationCode": {"description": "The confirmation code of the event reservation. This may also take the form of an \"order number\", \"confirmation number\", \"reservation number\", or other equivalent.", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#eventReservationInfo\"`.", "type": "string"}}, "type": "object"}, "EventSeat": {"id": "EventSeat", "properties": {"gate": {"$ref": "LocalizedString", "description": "The gate the ticket holder should enter to get to their seat, such as \"A\" or \"West\". This field is localizable so you may translate words or use different alphabets for the characters in an identifier."}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#eventSeat\"`.", "type": "string"}, "row": {"$ref": "LocalizedString", "description": "The row of the seat, such as \"1\", E\", \"BB\", or \"A5\". This field is localizable so you may translate words or use different alphabets for the characters in an identifier."}, "seat": {"$ref": "LocalizedString", "description": "The seat number, such as \"1\", \"2\", \"3\", or any other seat identifier. This field is localizable so you may translate words or use different alphabets for the characters in an identifier."}, "section": {"$ref": "LocalizedString", "description": "The section of the seat, such as \"121\". This field is localizable so you may translate words or use different alphabets for the characters in an identifier."}}, "type": "object"}, "EventTicketClass": {"id": "EventTicketClass", "properties": {"allowMultipleUsersPerObject": {"deprecated": true, "description": "Deprecated. Use `multipleDevicesAndHoldersAllowedStatus` instead.", "type": "boolean"}, "appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding object that will be used instead."}, "callbackOptions": {"$ref": "CallbackOptions", "description": "Callback options to be used to call the issuer back for every save/delete of an object for this class by the end-user. All objects of this class are eligible for the callback."}, "classTemplateInfo": {"$ref": "ClassTemplateInfo", "description": "Template information about how the class should be displayed. If unset, Google will fallback to a default set of fields to display."}, "confirmationCodeLabel": {"description": "The label to use for the confirmation code value (`eventTicketObject.reservationInfo.confirmationCode`) on the card detail view. Each available option maps to a set of localized strings, so that translations are shown to the user based on their locale. Both `confirmationCodeLabel` and `customConfirmationCodeLabel` may not be set. If neither is set, the label will default to \"Confirmation Code\", localized. If the confirmation code field is unset, this label will not be used.", "enum": ["CONFIRMATION_CODE_LABEL_UNSPECIFIED", "CONFIRMATION_CODE", "confirmationCode", "CONFIRMATION_NUMBER", "confirmationNumber", "ORDER_NUMBER", "orderNumber", "RESERVATION_NUMBER", "reservationNumber"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `CONFIRMATION_CODE`. Deprecated.", "", "Legacy alias for `CONFIRMATION_NUMBER`. Deprecated.", "", "Legacy alias for `ORDER_NUMBER`. Deprecated.", "", "Legacy alias for `RESERVATION_NUMBER`. Deprecated."], "type": "string"}, "countryCode": {"description": "Country code used to display the card's country (when the user is not in that country), as well as to display localized content when content is not available in the user's locale.", "type": "string"}, "customConfirmationCodeLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the confirmation code value (`eventTicketObject.reservationInfo.confirmationCode`) on the card detail view. This should only be used if the default \"Confirmation Code\" label or one of the `confirmationCodeLabel` options is not sufficient. Both `confirmationCodeLabel` and `customConfirmationCodeLabel` may not be set. If neither is set, the label will default to \"Confirmation Code\", localized. If the confirmation code field is unset, this label will not be used."}, "customGateLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the gate value (`eventTicketObject.seatInfo.gate`) on the card detail view. This should only be used if the default \"Gate\" label or one of the `gateLabel` options is not sufficient. Both `gateLabel` and `customGateLabel` may not be set. If neither is set, the label will default to \"Gate\", localized. If the gate field is unset, this label will not be used."}, "customRowLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the row value (`eventTicketObject.seatInfo.row`) on the card detail view. This should only be used if the default \"Row\" label or one of the `rowLabel` options is not sufficient. Both `rowLabel` and `customRowLabel` may not be set. If neither is set, the label will default to \"Row\", localized. If the row field is unset, this label will not be used."}, "customSeatLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the seat value (`eventTicketObject.seatInfo.seat`) on the card detail view. This should only be used if the default \"Seat\" label or one of the `seatLabel` options is not sufficient. Both `seatLabel` and `customSeatLabel` may not be set. If neither is set, the label will default to \"Seat\", localized. If the seat field is unset, this label will not be used."}, "customSectionLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the section value (`eventTicketObject.seatInfo.section`) on the card detail view. This should only be used if the default \"Section\" label or one of the `sectionLabel` options is not sufficient. Both `sectionLabel` and `customSectionLabel` may not be set. If neither is set, the label will default to \"Section\", localized. If the section field is unset, this label will not be used."}, "dateTime": {"$ref": "EventDateTime", "description": "The date & time information of the event."}, "enableSmartTap": {"description": "Identifies whether this class supports Smart Tap. The `redemptionIssuers` and object level `smartTapRedemptionLevel` fields must also be set up correctly in order for a pass to support Smart Tap.", "type": "boolean"}, "eventId": {"description": "The ID of the event. This ID should be unique for every event in an account. It is used to group tickets together if the user has saved multiple tickets for the same event. It can be at most 64 characters. If provided, the grouping will be stable. Be wary of unintentional collision to avoid grouping tickets that should not be grouped. If you use only one class per event, you can simply set this to the `classId` (with or without the issuer ID portion). If not provided, the platform will attempt to use other data to group tickets (potentially unstable).", "type": "string"}, "eventName": {"$ref": "LocalizedString", "description": "Required. The name of the event, such as \"LA Dodgers at SF Giants\"."}, "finePrint": {"$ref": "LocalizedString", "description": "The fine print, terms, or conditions of the ticket."}, "gateLabel": {"description": "The label to use for the gate value (`eventTicketObject.seatInfo.gate`) on the card detail view. Each available option maps to a set of localized strings, so that translations are shown to the user based on their locale. Both `gateLabel` and `customGateLabel` may not be set. If neither is set, the label will default to \"Gate\", localized. If the gate field is unset, this label will not be used.", "enum": ["GATE_LABEL_UNSPECIFIED", "GATE", "gate", "DOOR", "door", "ENTRANCE", "entrance"], "enumDeprecated": [false, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `GATE`. Deprecated.", "", "Legacy alias for `DO<PERSON>`. Deprecated.", "", "Legacy alias for `ENTRANC<PERSON>`. Deprecated."], "type": "string"}, "heroImage": {"$ref": "Image", "description": "Optional banner image displayed on the front of the card. If none is present, nothing will be displayed. The image will display at 100% width."}, "hexBackgroundColor": {"description": "The background color for the card. If not set the dominant color of the hero image is used, and if no hero image is set, the dominant color of the logo is used. The format is #rrggbb where rrggbb is a hex RGB triplet, such as `#ffcc00`. You can also use the shorthand version of the RGB triplet which is #rgb, such as `#fc0`.", "type": "string"}, "homepageUri": {"$ref": "<PERSON><PERSON>", "description": "The URI of your application's home page. Populating the URI in this field results in the exact same behavior as populating an URI in linksModuleData (when an object is rendered, a link to the homepage is shown in what would usually be thought of as the linksModuleData section of the object)."}, "id": {"description": "Required. The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "type": "string"}, "imageModulesData": {"description": "Image module data. The maximum number of these fields displayed is 1 from object level and 1 for class object level.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "infoModuleData": {"$ref": "InfoModuleData", "deprecated": true, "description": "Deprecated. Use textModulesData instead."}, "issuerName": {"description": "Required. The issuer name. Recommended maximum length is 20 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#eventTicketClass\"`.", "type": "string"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If links module data is also defined on the object, both will be displayed."}, "localizedIssuerName": {"$ref": "LocalizedString", "description": "Translated strings for the issuer_name. Recommended maximum length is 20 characters to ensure full string is displayed on smaller screens."}, "locations": {"deprecated": true, "description": "Note: This field is currently not supported to trigger geo notifications.", "items": {"$ref": "LatLongPoint"}, "type": "array"}, "logo": {"$ref": "Image", "description": "The logo image of the ticket. This image is displayed in the card detail view of the app."}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the class. Any additional MerchantLocations added beyond the 10 will be rejected. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "multipleDevicesAndHoldersAllowedStatus": {"description": "Identifies whether multiple users and devices will save the same object referencing this class.", "enum": ["STATUS_UNSPECIFIED", "MULTIPLE_HOLDERS", "ONE_USER_ALL_DEVICES", "ONE_USER_ONE_DEVICE", "multipleHolders", "oneUserAllDevices", "oneUserOneDevice"], "enumDeprecated": [false, false, false, false, true, true, true], "enumDescriptions": ["Unspecified preference.", "The Pass object is shareable by a user and can be saved by any number of different users, and on any number of devices. Partners typically use this setup for passes that do not need to be restricted to a single user or pinned to a single device.", "An object can only be saved by one user, but this user can view and use it on multiple of their devices. Once the first user saves the object, no other user will be allowed to view or save it.", "An object can only be saved by one user on a single device. Intended for use by select partners in limited circumstances. An example use case is a transit ticket that should be \"device pinned\", meaning it can be saved, viewed and used only by a single user on a single device. Contact support for additional information.", "Legacy alias for `MULTIP<PERSON>_HOLDERS`. Deprecated.", "Legacy alias for `ONE_USER_ALL_DEVICES`. Deprecated.", "Legacy alias for `ONE_USER_ONE_DEVICE`. Deprecated."], "type": "string"}, "notifyPreference": {"description": "Whether or not field updates to this class should trigger notifications. When set to NOTIFY, we will attempt to trigger a field update notification to users. These notifications will only be sent to users if the field is part of an allowlist. If not specified, no notification will be triggered. This setting is ephemeral and needs to be set with each PATCH or UPDATE request, otherwise a notification will not be triggered.", "enum": ["NOTIFICATION_SETTINGS_FOR_UPDATES_UNSPECIFIED", "NOTIFY_ON_UPDATE"], "enumDescriptions": ["Default behavior is no notifications sent.", "This value will result in a notification being sent, if the updated fields are part of an allowlist."], "type": "string"}, "redemptionIssuers": {"description": "Identifies which redemption issuers can redeem the pass over Smart Tap. Redemption issuers are identified by their issuer ID. Redemption issuers must have at least one Smart Tap key configured. The `enableSmartTap` and object level `smartTapRedemptionLevel` fields must also be set up correctly in order for a pass to support Smart Tap.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "review": {"$ref": "Review", "description": "The review comments set by the platform when a class is marked `approved` or `rejected`."}, "reviewStatus": {"description": "Required. The status of the class. This field can be set to `draft` or `underReview` using the insert, patch, or update API calls. Once the review state is changed from `draft` it may not be changed back to `draft`. You should keep this field to `draft` when the class is under development. A `draft` class cannot be used to create any object. You should set this field to `underReview` when you believe the class is ready for use. The platform will automatically set this field to `approved` and it can be immediately used to create or migrate objects. When updating an already `approved` class you should keep setting this field to `underReview`.", "enum": ["REVIEW_STATUS_UNSPECIFIED", "UNDER_REVIEW", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "APPROVED", "approved", "REJECTED", "rejected", "DRAFT", "draft"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `UNDER_REVIEW`. Deprecated.", "", "Legacy alias for `APPROVED`. Deprecated.", "", "Legacy alias for `REJECTED`. Deprecated.", "", "Legacy alias for `DRAFT`. Deprecated."], "type": "string"}, "rowLabel": {"description": "The label to use for the row value (`eventTicketObject.seatInfo.row`) on the card detail view. Each available option maps to a set of localized strings, so that translations are shown to the user based on their locale. Both `rowLabel` and `customRowLabel` may not be set. If neither is set, the label will default to \"Row\", localized. If the row field is unset, this label will not be used.", "enum": ["ROW_LABEL_UNSPECIFIED", "ROW", "row"], "enumDeprecated": [false, false, true], "enumDescriptions": ["", "", "Legacy alias for `<PERSON><PERSON>`. Deprecated."], "type": "string"}, "seatLabel": {"description": "The label to use for the seat value (`eventTicketObject.seatInfo.seat`) on the card detail view. Each available option maps to a set of localized strings, so that translations are shown to the user based on their locale. Both `seatLabel` and `customSeatLabel` may not be set. If neither is set, the label will default to \"Seat\", localized. If the seat field is unset, this label will not be used.", "enum": ["SEAT_LABEL_UNSPECIFIED", "SEAT", "seat"], "enumDeprecated": [false, false, true], "enumDescriptions": ["", "", "Legacy alias for `SEAT`. Deprecated."], "type": "string"}, "sectionLabel": {"description": "The label to use for the section value (`eventTicketObject.seatInfo.section`) on the card detail view. Each available option maps to a set of localized strings, so that translations are shown to the user based on their locale. Both `sectionLabel` and `customSectionLabel` may not be set. If neither is set, the label will default to \"Section\", localized. If the section field is unset, this label will not be used.", "enum": ["SECTION_LABEL_UNSPECIFIED", "SECTION", "section", "THEATER", "theater"], "enumDeprecated": [false, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `SECTION`. Deprecated.", "", "Legacy alias for `THEATER`. Deprecated."], "type": "string"}, "securityAnimation": {"$ref": "SecurityAnimation", "description": "Optional information about the security animation. If this is set a security animation will be rendered on pass details."}, "textModulesData": {"description": "Text module data. If text module data is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from the object and 10 from the class.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the class. For a pass only ten will be displayed, prioritizing those from the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "venue": {"$ref": "EventVenue", "description": "Event venue details."}, "version": {"deprecated": true, "description": "Deprecated", "format": "int64", "type": "string"}, "viewUnlockRequirement": {"description": "View Unlock Requirement options for the event ticket.", "enum": ["VIEW_UNLOCK_REQUIREMENT_UNSPECIFIED", "UNLOCK_NOT_REQUIRED", "UNLOCK_REQUIRED_TO_VIEW"], "enumDescriptions": ["Default value, same as UNLOCK_NOT_REQUIRED.", "Default behavior for all the existing Passes if ViewUnlockRequirement is not set.", "Requires the user to unlock their device each time the pass is viewed. If the user removes their device lock after saving the pass, then they will be prompted to create a device lock before the pass can be viewed."], "type": "string"}, "wideLogo": {"$ref": "Image", "description": "The wide logo of the ticket. When provided, this will be used in place of the logo in the top left of the card view."}, "wordMark": {"$ref": "Image", "deprecated": true, "description": "Deprecated."}}, "type": "object"}, "EventTicketClassAddMessageResponse": {"id": "EventTicketClassAddMessageResponse", "properties": {"resource": {"$ref": "EventTicketClass", "description": "The updated EventTicketClass resource."}}, "type": "object"}, "EventTicketClassListResponse": {"id": "EventTicketClassListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "EventTicketClass"}, "type": "array"}}, "type": "object"}, "EventTicketObject": {"id": "EventTicketObject", "properties": {"appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding class only object AppLinkData will be displayed."}, "barcode": {"$ref": "Barcode", "description": "The barcode type and value."}, "classId": {"description": "Required. The class associated with this object. The class must be of the same type as this object, must already exist, and must be approved. Class IDs should follow the format issuer ID.identifier where the former is issued by Google and latter is chosen by you.", "type": "string"}, "classReference": {"$ref": "EventTicketClass", "description": "A copy of the inherited fields of the parent class. These fields are retrieved during a GET."}, "disableExpirationNotification": {"description": "Indicates if notifications should explicitly be suppressed. If this field is set to true, regardless of the `messages` field, expiration notifications to the user will be suppressed. By default, this field is set to false. Currently, this can only be set for offers.", "type": "boolean"}, "faceValue": {"$ref": "Money", "description": "The face value of the ticket, matching what would be printed on a physical version of the ticket."}, "groupingInfo": {"$ref": "GroupingInfo", "description": "Information that controls how passes are grouped together."}, "hasLinkedDevice": {"description": "Whether this object is currently linked to a single device. This field is set by the platform when a user saves the object, linking it to their device. Intended for use by select partners. Contact support for additional information.", "type": "boolean"}, "hasUsers": {"description": "Indicates if the object has users. This field is set by the platform.", "type": "boolean"}, "heroImage": {"$ref": "Image", "description": "Optional banner image displayed on the front of the card. If none is present, hero image of the class, if present, will be displayed. If hero image of the class is also not present, nothing will be displayed."}, "hexBackgroundColor": {"description": "The background color for the card. If not set the dominant color of the hero image is used, and if no hero image is set, the dominant color of the logo is used. The format is #rrggbb where rrggbb is a hex RGB triplet, such as `#ffcc00`. You can also use the shorthand version of the RGB triplet which is #rgb, such as `#fc0`.", "type": "string"}, "id": {"description": "Required. The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID.identifier where the former is issued by Google and latter is chosen by you. The unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "type": "string"}, "imageModulesData": {"description": "Image module data. The maximum number of these fields displayed is 1 from object level and 1 for class object level.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "infoModuleData": {"$ref": "InfoModuleData", "description": "Deprecated. Use textModulesData instead."}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#eventTicketObject\"`.", "type": "string"}, "linkedObjectIds": {"description": "linked_object_ids are a list of other objects such as event ticket, loyalty, offer, generic, giftcard, transit and boarding pass that should be automatically attached to this event ticket object. If a user had saved this event ticket, then these linked_object_ids would be automatically pushed to the user's wallet (unless they turned off the setting to receive such linked passes). Make sure that objects present in linked_object_ids are already inserted - if not, calls would fail. Once linked, the linked objects cannot be unlinked. You cannot link objects belonging to another issuer. There is a limit to the number of objects that can be linked to a single object. After the limit is reached, new linked objects in the call will be ignored silently. Object IDs should follow the format issuer ID. identifier where the former is issued by Google and the latter is chosen by you.", "items": {"type": "string"}, "type": "array"}, "linkedOfferIds": {"description": "A list of offer objects linked to this event ticket. The offer objects must already exist. Offer object IDs should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you.", "items": {"type": "string"}, "type": "array"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If links module data is also defined on the class, both will be displayed."}, "locations": {"deprecated": true, "description": "Note: This field is currently not supported to trigger geo notifications.", "items": {"$ref": "LatLongPoint"}, "type": "array"}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the object. Any additional MerchantLocations added beyond the 10 will be rejected. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "notifyPreference": {"description": "Whether or not field updates to this object should trigger notifications. When set to NOTIFY, we will attempt to trigger a field update notification to users. These notifications will only be sent to users if the field is part of an allowlist. If set to DO_NOT_NOTIFY or NOTIFICATION_SETTINGS_UNSPECIFIED, no notification will be triggered. This setting is ephemeral and needs to be set with each PATCH or UPDATE request, otherwise a notification will not be triggered.", "enum": ["NOTIFICATION_SETTINGS_FOR_UPDATES_UNSPECIFIED", "NOTIFY_ON_UPDATE"], "enumDescriptions": ["Default behavior is no notifications sent.", "This value will result in a notification being sent, if the updated fields are part of an allowlist."], "type": "string"}, "passConstraints": {"$ref": "PassConstraints", "description": "Pass constraints for the object. Includes limiting NFC and screenshot behaviors."}, "reservationInfo": {"$ref": "EventReservationInfo", "description": "Reservation details for this ticket. This is expected to be shared amongst all tickets that were purchased in the same order."}, "rotatingBarcode": {"$ref": "RotatingBarcode", "description": "The rotating barcode type and value."}, "saveRestrictions": {"$ref": "SaveRestrictions", "description": "Restrictions on the object that needs to be verified before the user tries to save the pass. Note that this restrictions will only be applied during save time. If the restrictions changed after a user saves the pass, the new restrictions will not be applied to an already saved pass."}, "seatInfo": {"$ref": "EventSeat", "description": "Seating details for this ticket."}, "smartTapRedemptionValue": {"description": "The value that will be transmitted to a Smart Tap certified terminal over NFC for this object. The class level fields `enableSmartTap` and `redemptionIssuers` must also be set up correctly in order for the pass to support Smart Tap. Only ASCII characters are supported.", "type": "string"}, "state": {"description": "Required. The state of the object. This field is used to determine how an object is displayed in the app. For example, an `inactive` object is moved to the \"Expired passes\" section.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "active", "COMPLETED", "completed", "EXPIRED", "expired", "INACTIVE", "inactive"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["Default value.", "Object is active and displayed to with other active objects.", "Legacy alias for `ACTIVE`. Deprecated.", "Object has completed it's lifecycle.", "Legacy alias for `COMPLETED`. Deprecated.", "Object is no longer valid (`validTimeInterval` passed).", "Legacy alias for `EXPIRED`. Deprecated.", "Object is no longer valid", "Legacy alias for `INACTIVE`. Deprecated."], "type": "string"}, "textModulesData": {"description": "Text module data. If text module data is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from the object and 10 from the class.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "ticketHolderName": {"description": "Name of the ticket holder, if the ticket is assigned to a person. E.g. \"<PERSON>\" or \"<PERSON>\".", "type": "string"}, "ticketNumber": {"description": "The number of the ticket. This can be a unique identifier across all tickets in an issuer's system, all tickets for the event (e.g. XYZ1234512345), or all tickets in the order (1, 2, 3, etc.).", "type": "string"}, "ticketType": {"$ref": "LocalizedString", "description": "The type of the ticket, such as \"Adult\" or \"Child\", or \"VIP\" or \"Standard\"."}, "validTimeInterval": {"$ref": "TimeInterval", "description": "The time period this object will be `active` and object can be used. An object's state will be changed to `expired` when this time period has passed."}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "version": {"deprecated": true, "description": "Deprecated", "format": "int64", "type": "string"}}, "type": "object"}, "EventTicketObjectAddMessageResponse": {"id": "EventTicketObjectAddMessageResponse", "properties": {"resource": {"$ref": "EventTicketObject", "description": "The updated EventTicketObject resource."}}, "type": "object"}, "EventTicketObjectListResponse": {"id": "EventTicketObjectListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "EventTicketObject"}, "type": "array"}}, "type": "object"}, "EventVenue": {"id": "EventVenue", "properties": {"address": {"$ref": "LocalizedString", "description": "The address of the venue, such as \"24 Willie Mays Plaza\\nSan Francisco, CA 94107\". Address lines are separated by line feed (`\\n`) characters. This is required."}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#eventVenue\"`.", "type": "string"}, "name": {"$ref": "LocalizedString", "description": "The name of the venue, such as \"AT&T Park\". This is required."}}, "type": "object"}, "ExpiryNotification": {"description": "Indicates that the issuer would like Google Wallet to send expiry notifications 2 days prior to the card expiration.", "id": "ExpiryNotification", "properties": {"enableNotification": {"description": "Indicates if the object needs to have expiry notification enabled.", "type": "boolean"}}, "type": "object"}, "FieldReference": {"description": "Reference definition to use with field overrides.", "id": "FieldReference", "properties": {"dateFormat": {"description": "Only valid if the `fieldPath` references a date field. Chooses how the date field will be formatted and displayed in the UI.", "enum": ["DATE_FORMAT_UNSPECIFIED", "DATE_TIME", "dateTime", "DATE_ONLY", "dateOnly", "TIME_ONLY", "timeOnly", "DATE_TIME_YEAR", "dateTimeYear", "DATE_YEAR", "dateYear", "YEAR_MONTH", "YEAR_MONTH_DAY"], "enumDeprecated": [false, false, true, false, true, false, true, false, true, false, true, false, false], "enumDescriptions": ["Default option when no format is specified, when selected, no formatting will be applied.", "Renders `2018-12-14T13:00:00` as `Dec 14, 1:00 PM` in `en_US`.", "Legacy alias for `DATE_TIME`. Deprecated.", "Renders `2018-12-14T13:00:00` as `Dec 14` in `en_US`.", "Legacy alias for `<PERSON>AT<PERSON>_ONLY`. Deprecated.", "Renders `2018-12-14T13:00:00` as `1:00 PM` in `en_US`.", "Legacy alias for `TIME_ONLY`. Deprecated.", "Renders `2018-12-14T13:00:00` as `Dec 14, 2018, 1:00 PM` in `en_US`.", "Legacy alias for `DATE_TIME_YEAR`. Deprecated.", "Renders `2018-12-14T13:00:00` as `Dec 14, 2018` in `en_US`.", "Legacy alias for `DATE_YEAR`. Deprecated.", "Renders `2018-12-14T13:00:00` as `2018-12`.", "Renders `2018-12-14T13:00:00` as `2018-12-14`."], "type": "string"}, "fieldPath": {"description": "Path to the field being referenced, prefixed with \"object\" or \"class\" and separated with dots. For example, it may be the string \"object.purchaseDetails.purchasePrice\".", "type": "string"}}, "type": "object"}, "FieldSelector": {"description": "Custom field selector to use with field overrides.", "id": "FieldSelector", "properties": {"fields": {"description": "If more than one reference is supplied, then the first one that references a non-empty field will be displayed.", "items": {"$ref": "FieldReference"}, "type": "array"}}, "type": "object"}, "FirstRowOption": {"id": "FirstRowOption", "properties": {"fieldOption": {"$ref": "FieldSelector", "description": "A reference to the field to be displayed in the first row."}, "transitOption": {"enum": ["TRANSIT_OPTION_UNSPECIFIED", "ORIGIN_AND_DESTINATION_NAMES", "originAndDestinationNames", "ORIGIN_AND_DESTINATION_CODES", "originAndDestinationCodes", "ORIGIN_NAME", "originName"], "enumDeprecated": [false, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `OR<PERSON>IN_AND_DESTINATION_NAMES`. Deprecated.", "", "Legacy alias for `OR<PERSON>IN_AND_DESTINATION_CODES`. Deprecated.", "", "Legacy alias for `ORIGIN_NAME`. Deprecated."], "type": "string"}}, "type": "object"}, "FlightCarrier": {"id": "FlightCarrier", "properties": {"airlineAllianceLogo": {"$ref": "Image", "description": "A logo for the airline alliance, displayed below the QR code that the passenger scans to board."}, "airlineLogo": {"$ref": "Image", "description": "A logo for the airline described by carrierIataCode and localizedAirlineName. This logo will be rendered at the top of the detailed card view."}, "airlineName": {"$ref": "LocalizedString", "description": "A localized name of the airline specified by carrierIataCode. If unset, `issuer_name` or `localized_issuer_name` from `FlightClass` will be used for display purposes. eg: \"Swiss Air\" for \"LX\""}, "carrierIataCode": {"description": "Two character IATA airline code of the marketing carrier (as opposed to operating carrier). Exactly one of this or `carrierIcaoCode` needs to be provided for `carrier` and `operatingCarrier`. eg: \"LX\" for Swiss Air", "type": "string"}, "carrierIcaoCode": {"description": "Three character ICAO airline code of the marketing carrier (as opposed to operating carrier). Exactly one of this or `carrierIataCode` needs to be provided for `carrier` and `operatingCarrier`. eg: \"EZY\" for Easy Jet", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#flightCarrier\"`.", "type": "string"}, "wideAirlineLogo": {"$ref": "Image", "description": "The wide logo of the airline. When provided, this will be used in place of the airline logo in the top left of the card view."}}, "type": "object"}, "FlightClass": {"id": "FlightClass", "properties": {"allowMultipleUsersPerObject": {"deprecated": true, "description": "Deprecated. Use `multipleDevicesAndHoldersAllowedStatus` instead.", "type": "boolean"}, "appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding object that will be used instead."}, "boardingAndSeatingPolicy": {"$ref": "BoardingAndSeatingPolicy", "description": "Policies for boarding and seating. These will inform which labels will be shown to users."}, "callbackOptions": {"$ref": "CallbackOptions", "description": "Callback options to be used to call the issuer back for every save/delete of an object for this class by the end-user. All objects of this class are eligible for the callback."}, "classTemplateInfo": {"$ref": "ClassTemplateInfo", "description": "Template information about how the class should be displayed. If unset, Google will fallback to a default set of fields to display."}, "countryCode": {"description": "Country code used to display the card's country (when the user is not in that country), as well as to display localized content when content is not available in the user's locale.", "type": "string"}, "destination": {"$ref": "AirportInfo", "description": "Required. Destination airport."}, "enableSmartTap": {"description": "Identifies whether this class supports Smart Tap. The `redemptionIssuers` and object level `smartTapRedemptionLevel` fields must also be set up correctly in order for a pass to support Smart Tap.", "type": "boolean"}, "flightHeader": {"$ref": "FlightHeader", "description": "Required. Information about the flight carrier and number."}, "flightStatus": {"description": "Status of this flight. If unset, Google will compute status based on data from other sources, such as FlightStats, etc. Note: Google-computed status will not be returned in API responses.", "enum": ["FLIGHT_STATUS_UNSPECIFIED", "SCHEDULED", "scheduled", "ACTIVE", "active", "LANDED", "landed", "CANCELLED", "cancelled", "REDIRECTED", "redirected", "DIVERTED", "diverted"], "enumDeprecated": [false, false, true, false, true, false, true, false, true, false, true, false, true], "enumDescriptions": ["", "Flight is on time, early, or delayed.", "Legacy alias for `SCHEDULED`. Deprecated.", "Flight is in progress (taxiing, taking off, landing, airborne).", "Legacy alias for `ACTIVE`. Deprecated.", "Flight landed at the original destination.", "Legacy alias for `LANDED`. Deprecated.", "Flight is cancelled.", "Legacy alias for `CANCELLED`. Deprecated.", "Flight is airborne but heading to a different airport than the original destination.", "Legacy alias for `REDIRECTED`. Deprecated.", "Flight has already landed at a different airport than the original destination.", "Legacy alias for `DIVERTED`. Deprecated."], "type": "string"}, "heroImage": {"$ref": "Image", "description": "Optional banner image displayed on the front of the card. If none is present, nothing will be displayed. The image will display at 100% width."}, "hexBackgroundColor": {"description": "The background color for the card. If not set the dominant color of the hero image is used, and if no hero image is set, the dominant color of the logo is used. The format is #rrggbb where rrggbb is a hex RGB triplet, such as `#ffcc00`. You can also use the shorthand version of the RGB triplet which is #rgb, such as `#fc0`.", "type": "string"}, "homepageUri": {"$ref": "<PERSON><PERSON>", "description": "The URI of your application's home page. Populating the URI in this field results in the exact same behavior as populating an URI in linksModuleData (when an object is rendered, a link to the homepage is shown in what would usually be thought of as the linksModuleData section of the object)."}, "id": {"description": "Required. The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "type": "string"}, "imageModulesData": {"description": "Image module data. The maximum number of these fields displayed is 1 from object level and 1 for class object level.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "infoModuleData": {"$ref": "InfoModuleData", "deprecated": true, "description": "Deprecated. Use textModulesData instead."}, "issuerName": {"description": "Required. The issuer name. Recommended maximum length is 20 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#flightClass\"`.", "type": "string"}, "languageOverride": {"description": "If this field is present, boarding passes served to a user's device will always be in this language. Represents the BCP 47 language tag. Example values are \"en-US\", \"en-GB\", \"de\", or \"de-AT\".", "type": "string"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If links module data is also defined on the object, both will be displayed."}, "localBoardingDateTime": {"description": "The boarding time as it would be printed on the boarding pass. This is an ISO 8601 extended format date/time without an offset. Time may be specified up to millisecond precision. eg: `2027-03-05T06:30:00` This should be the local date/time at the airport (not a UTC time). Google will reject the request if UTC offset is provided. Time zones will be calculated by Google based on departure airport.", "type": "string"}, "localEstimatedOrActualArrivalDateTime": {"description": "The estimated time the aircraft plans to reach the destination gate (not the runway) or the actual time it reached the gate. This field should be set if at least one of the below is true: - It differs from the scheduled time. Google will use it to calculate the delay. - The aircraft already arrived at the gate. Google will use it to inform the user that the flight has arrived at the gate. This is an ISO 8601 extended format date/time without an offset. Time may be specified up to millisecond precision. eg: `2027-03-05T06:30:00` This should be the local date/time at the airport (not a UTC time). Google will reject the request if UTC offset is provided. Time zones will be calculated by Google based on arrival airport.", "type": "string"}, "localEstimatedOrActualDepartureDateTime": {"description": "The estimated time the aircraft plans to pull from the gate or the actual time the aircraft already pulled from the gate. Note: This is not the runway time. This field should be set if at least one of the below is true: - It differs from the scheduled time. Google will use it to calculate the delay. - The aircraft already pulled from the gate. Google will use it to inform the user when the flight actually departed. This is an ISO 8601 extended format date/time without an offset. Time may be specified up to millisecond precision. eg: `2027-03-05T06:30:00` This should be the local date/time at the airport (not a UTC time). Google will reject the request if UTC offset is provided. Time zones will be calculated by Google based on departure airport.", "type": "string"}, "localGateClosingDateTime": {"description": "The gate closing time as it would be printed on the boarding pass. Do not set this field if you do not want to print it in the boarding pass. This is an ISO 8601 extended format date/time without an offset. Time may be specified up to millisecond precision. eg: `2027-03-05T06:30:00` This should be the local date/time at the airport (not a UTC time). Google will reject the request if UTC offset is provided. Time zones will be calculated by Google based on departure airport.", "type": "string"}, "localScheduledArrivalDateTime": {"description": "The scheduled time the aircraft plans to reach the destination gate (not the runway). Note: This field should not change too close to the flight time. For updates to departure times (delays, etc), please set `localEstimatedOrActualArrivalDateTime`. This is an ISO 8601 extended format date/time without an offset. Time may be specified up to millisecond precision. eg: `2027-03-05T06:30:00` This should be the local date/time at the airport (not a UTC time). Google will reject the request if UTC offset is provided. Time zones will be calculated by Google based on arrival airport.", "type": "string"}, "localScheduledDepartureDateTime": {"description": "Required. The scheduled date and time when the aircraft is expected to depart the gate (not the runway) Note: This field should not change too close to the departure time. For updates to departure times (delays, etc), please set `localEstimatedOrActualDepartureDateTime`. This is an ISO 8601 extended format date/time without an offset. Time may be specified up to millisecond precision. eg: `2027-03-05T06:30:00` This should be the local date/time at the airport (not a UTC time). Google will reject the request if UTC offset is provided. Time zones will be calculated by Google based on departure airport.", "type": "string"}, "localizedIssuerName": {"$ref": "LocalizedString", "description": "Translated strings for the issuer_name. Recommended maximum length is 20 characters to ensure full string is displayed on smaller screens."}, "locations": {"deprecated": true, "description": "Note: This field is currently not supported to trigger geo notifications.", "items": {"$ref": "LatLongPoint"}, "type": "array"}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the class. Any additional MerchantLocations added beyond the 10 will be rejected by the validator. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "multipleDevicesAndHoldersAllowedStatus": {"description": "Identifies whether multiple users and devices will save the same object referencing this class.", "enum": ["STATUS_UNSPECIFIED", "MULTIPLE_HOLDERS", "ONE_USER_ALL_DEVICES", "ONE_USER_ONE_DEVICE", "multipleHolders", "oneUserAllDevices", "oneUserOneDevice"], "enumDeprecated": [false, false, false, false, true, true, true], "enumDescriptions": ["Unspecified preference.", "The Pass object is shareable by a user and can be saved by any number of different users, and on any number of devices. Partners typically use this setup for passes that do not need to be restricted to a single user or pinned to a single device.", "An object can only be saved by one user, but this user can view and use it on multiple of their devices. Once the first user saves the object, no other user will be allowed to view or save it.", "An object can only be saved by one user on a single device. Intended for use by select partners in limited circumstances. An example use case is a transit ticket that should be \"device pinned\", meaning it can be saved, viewed and used only by a single user on a single device. Contact support for additional information.", "Legacy alias for `MULTIP<PERSON>_HOLDERS`. Deprecated.", "Legacy alias for `ONE_USER_ALL_DEVICES`. Deprecated.", "Legacy alias for `ONE_USER_ONE_DEVICE`. Deprecated."], "type": "string"}, "notifyPreference": {"description": "Whether or not field updates to this class should trigger notifications. When set to NOTIFY, we will attempt to trigger a field update notification to users. These notifications will only be sent to users if the field is part of an allowlist. If not specified, no notification will be triggered. This setting is ephemeral and needs to be set with each PATCH or UPDATE request, otherwise a notification will not be triggered.", "enum": ["NOTIFICATION_SETTINGS_FOR_UPDATES_UNSPECIFIED", "NOTIFY_ON_UPDATE"], "enumDescriptions": ["Default behavior is no notifications sent.", "This value will result in a notification being sent, if the updated fields are part of an allowlist."], "type": "string"}, "origin": {"$ref": "AirportInfo", "description": "Required. Origin airport."}, "redemptionIssuers": {"description": "Identifies which redemption issuers can redeem the pass over Smart Tap. Redemption issuers are identified by their issuer ID. Redemption issuers must have at least one Smart Tap key configured. The `enableSmartTap` and object level `smartTapRedemptionLevel` fields must also be set up correctly in order for a pass to support Smart Tap.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "review": {"$ref": "Review", "description": "The review comments set by the platform when a class is marked `approved` or `rejected`."}, "reviewStatus": {"description": "Required. The status of the class. This field can be set to `draft` or `underReview` using the insert, patch, or update API calls. Once the review state is changed from `draft` it may not be changed back to `draft`. You should keep this field to `draft` when the class is under development. A `draft` class cannot be used to create any object. You should set this field to `underReview` when you believe the class is ready for use. The platform will automatically set this field to `approved` and it can be immediately used to create or migrate objects. When updating an already `approved` class you should keep setting this field to `underReview`.", "enum": ["REVIEW_STATUS_UNSPECIFIED", "UNDER_REVIEW", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "APPROVED", "approved", "REJECTED", "rejected", "DRAFT", "draft"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `UNDER_REVIEW`. Deprecated.", "", "Legacy alias for `APPROVED`. Deprecated.", "", "Legacy alias for `REJECTED`. Deprecated.", "", "Legacy alias for `DRAFT`. Deprecated."], "type": "string"}, "securityAnimation": {"$ref": "SecurityAnimation", "description": "Optional information about the security animation. If this is set a security animation will be rendered on pass details."}, "textModulesData": {"description": "Text module data. If text module data is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from the object and 10 from the class.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the class. For a pass only ten will be displayed, prioritizing those from the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "version": {"deprecated": true, "description": "Deprecated", "format": "int64", "type": "string"}, "viewUnlockRequirement": {"description": "View Unlock Requirement options for the boarding pass.", "enum": ["VIEW_UNLOCK_REQUIREMENT_UNSPECIFIED", "UNLOCK_NOT_REQUIRED", "UNLOCK_REQUIRED_TO_VIEW"], "enumDescriptions": ["Default value, same as UNLOCK_NOT_REQUIRED.", "Default behavior for all the existing Passes if ViewUnlockRequirement is not set.", "Requires the user to unlock their device each time the pass is viewed. If the user removes their device lock after saving the pass, then they will be prompted to create a device lock before the pass can be viewed."], "type": "string"}, "wordMark": {"$ref": "Image", "deprecated": true, "description": "Deprecated."}}, "type": "object"}, "FlightClassAddMessageResponse": {"id": "FlightClassAddMessageResponse", "properties": {"resource": {"$ref": "FlightClass", "description": "The updated FlightClass resource."}}, "type": "object"}, "FlightClassListResponse": {"id": "FlightClassListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "FlightClass"}, "type": "array"}}, "type": "object"}, "FlightHeader": {"id": "FlightHeader", "properties": {"carrier": {"$ref": "FlightCarrier", "description": "Information about airline carrier. This is a required property of `flightHeader`."}, "flightNumber": {"description": "The flight number without IATA carrier code. This field should contain only digits. This is a required property of `flightHeader`. eg: \"123\"", "type": "string"}, "flightNumberDisplayOverride": {"description": "Override value to use for flight number. The default value used for display purposes is carrier + flight_number. If a different value needs to be shown to passengers, use this field to override the default behavior. eg: \"XX1234 / YY576\"", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#flightHeader\"`.", "type": "string"}, "operatingCarrier": {"$ref": "FlightCarrier", "description": "Information about operating airline carrier."}, "operatingFlightNumber": {"description": "The flight number used by the operating carrier without IATA carrier code. This field should contain only digits. eg: \"234\"", "type": "string"}}, "type": "object"}, "FlightObject": {"id": "FlightObject", "properties": {"appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding class only object AppLinkData will be displayed."}, "barcode": {"$ref": "Barcode", "description": "The barcode type and value."}, "boardingAndSeatingInfo": {"$ref": "BoardingAndSeatingInfo", "description": "Passenger specific information about boarding and seating."}, "classId": {"description": "Required. The class associated with this object. The class must be of the same type as this object, must already exist, and must be approved. Class IDs should follow the format issuer ID.identifier where the former is issued by Google and latter is chosen by you.", "type": "string"}, "classReference": {"$ref": "FlightClass", "description": "A copy of the inherited fields of the parent class. These fields are retrieved during a GET."}, "disableExpirationNotification": {"description": "Indicates if notifications should explicitly be suppressed. If this field is set to true, regardless of the `messages` field, expiration notifications to the user will be suppressed. By default, this field is set to false. Currently, this can only be set for Flights.", "type": "boolean"}, "groupingInfo": {"$ref": "GroupingInfo", "description": "Information that controls how passes are grouped together."}, "hasLinkedDevice": {"description": "Whether this object is currently linked to a single device. This field is set by the platform when a user saves the object, linking it to their device. Intended for use by select partners. Contact support for additional information.", "type": "boolean"}, "hasUsers": {"description": "Indicates if the object has users. This field is set by the platform.", "type": "boolean"}, "heroImage": {"$ref": "Image", "description": "Optional banner image displayed on the front of the card. If none is present, hero image of the class, if present, will be displayed. If hero image of the class is also not present, nothing will be displayed."}, "hexBackgroundColor": {"description": "The background color for the card. If not set the dominant color of the hero image is used, and if no hero image is set, the dominant color of the logo is used. The format is #rrggbb where rrggbb is a hex RGB triplet, such as `#ffcc00`. You can also use the shorthand version of the RGB triplet which is #rgb, such as `#fc0`.", "type": "string"}, "id": {"description": "Required. The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID.identifier where the former is issued by Google and latter is chosen by you. The unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "type": "string"}, "imageModulesData": {"description": "Image module data. The maximum number of these fields displayed is 1 from object level and 1 for class object level.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "infoModuleData": {"$ref": "InfoModuleData", "description": "Deprecated. Use textModulesData instead."}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#flightObject\"`.", "type": "string"}, "linkedObjectIds": {"description": "linked_object_ids are a list of other objects such as event ticket, loyalty, offer, generic, giftcard, transit and boarding pass that should be automatically attached to this flight object. If a user had saved this boarding pass, then these linked_object_ids would be automatically pushed to the user's wallet (unless they turned off the setting to receive such linked passes). Make sure that objects present in linked_object_ids are already inserted - if not, calls would fail. Once linked, the linked objects cannot be unlinked. You cannot link objects belonging to another issuer. There is a limit to the number of objects that can be linked to a single object. After the limit is reached, new linked objects in the call will be ignored silently. Object IDs should follow the format issuer ID. identifier where the former is issued by Google and the latter is chosen by you.", "items": {"type": "string"}, "type": "array"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If links module data is also defined on the class, both will be displayed."}, "locations": {"deprecated": true, "description": "Note: This field is currently not supported to trigger geo notifications.", "items": {"$ref": "LatLongPoint"}, "type": "array"}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the object. Any additional MerchantLocations added beyond the 10 will be rejected. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "notifyPreference": {"description": "Whether or not field updates to this object should trigger notifications. When set to NOTIFY, we will attempt to trigger a field update notification to users. These notifications will only be sent to users if the field is part of an allowlist. If set to DO_NOT_NOTIFY or NOTIFICATION_SETTINGS_UNSPECIFIED, no notification will be triggered. This setting is ephemeral and needs to be set with each PATCH or UPDATE request, otherwise a notification will not be triggered.", "enum": ["NOTIFICATION_SETTINGS_FOR_UPDATES_UNSPECIFIED", "NOTIFY_ON_UPDATE"], "enumDescriptions": ["Default behavior is no notifications sent.", "This value will result in a notification being sent, if the updated fields are part of an allowlist."], "type": "string"}, "passConstraints": {"$ref": "PassConstraints", "description": "Pass constraints for the object. Includes limiting NFC and screenshot behaviors."}, "passengerName": {"description": "Required. Passenger name as it would appear on the boarding pass. eg: \"<PERSON>\" or \"<PERSON><PERSON>/<PERSON>\" or \"GAHAN/DAVEM\"", "type": "string"}, "reservationInfo": {"$ref": "ReservationInfo", "description": "Required. Information about flight reservation."}, "rotatingBarcode": {"$ref": "RotatingBarcode", "description": "The rotating barcode type and value."}, "saveRestrictions": {"$ref": "SaveRestrictions", "description": "Restrictions on the object that needs to be verified before the user tries to save the pass. Note that this restrictions will only be applied during save time. If the restrictions changed after a user saves the pass, the new restrictions will not be applied to an already saved pass."}, "securityProgramLogo": {"$ref": "Image", "description": "An image for the security program that applies to the passenger."}, "smartTapRedemptionValue": {"description": "The value that will be transmitted to a Smart Tap certified terminal over NFC for this object. The class level fields `enableSmartTap` and `redemptionIssuers` must also be set up correctly in order for the pass to support Smart Tap. Only ASCII characters are supported.", "type": "string"}, "state": {"description": "Required. The state of the object. This field is used to determine how an object is displayed in the app. For example, an `inactive` object is moved to the \"Expired passes\" section.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "active", "COMPLETED", "completed", "EXPIRED", "expired", "INACTIVE", "inactive"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["Default value.", "Object is active and displayed to with other active objects.", "Legacy alias for `ACTIVE`. Deprecated.", "Object has completed it's lifecycle.", "Legacy alias for `COMPLETED`. Deprecated.", "Object is no longer valid (`validTimeInterval` passed).", "Legacy alias for `EXPIRED`. Deprecated.", "Object is no longer valid", "Legacy alias for `INACTIVE`. Deprecated."], "type": "string"}, "textModulesData": {"description": "Text module data. If text module data is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from the object and 10 from the class.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "validTimeInterval": {"$ref": "TimeInterval", "description": "The time period this object will be `active` and object can be used. An object's state will be changed to `expired` when this time period has passed."}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "version": {"deprecated": true, "description": "Deprecated", "format": "int64", "type": "string"}}, "type": "object"}, "FlightObjectAddMessageResponse": {"id": "FlightObjectAddMessageResponse", "properties": {"resource": {"$ref": "FlightObject", "description": "The updated FlightObject resource."}}, "type": "object"}, "FlightObjectListResponse": {"id": "FlightObjectListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "FlightObject"}, "type": "array"}}, "type": "object"}, "FrequentFlyerInfo": {"id": "FrequentFlyerInfo", "properties": {"frequentFlyerNumber": {"description": "Frequent flyer number. Required for each nested object of kind `walletobjects#frequentFlyerInfo`.", "type": "string"}, "frequentFlyerProgramName": {"$ref": "LocalizedString", "description": "Frequent flyer program name. eg: \"Lufthansa Miles & More\""}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#frequentFlyerInfo\"`.", "type": "string"}}, "type": "object"}, "GenericClass": {"description": "Generic Class", "id": "GenericClass", "properties": {"appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding object that will be used instead."}, "callbackOptions": {"$ref": "CallbackOptions", "description": "Callback options to be used to call the issuer back for every save/delete of an object for this class by the end-user. All objects of this class are eligible for the callback."}, "classTemplateInfo": {"$ref": "ClassTemplateInfo", "description": "Template information about how the class should be displayed. If unset, Google will fallback to a default set of fields to display."}, "enableSmartTap": {"description": "Available only to Smart Tap enabled partners. Contact support for additional guidance.", "type": "boolean"}, "id": {"description": "Required. The unique identifier for the class. This ID must be unique across all from an issuer. This value needs to follow the format `issuerID.identifier` where `issuerID` is issued by Google and `identifier` is chosen by you. The unique identifier can only include alphanumeric characters, `.`, `_`, or `-`.", "type": "string"}, "imageModulesData": {"description": "Image module data. If `imageModulesData` is also defined on the object, both will be displayed. Only one of the image from class and one from object level will be rendered when both set.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If `linksModuleData` is also defined on the object, both will be displayed. The maximum number of these fields displayed is 10 from class and 10 from object."}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the class. Any additional MerchantLocations added beyond the 10 will be rejected. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "multipleDevicesAndHoldersAllowedStatus": {"description": "Identifies whether multiple users and devices will save the same object referencing this class.", "enum": ["STATUS_UNSPECIFIED", "MULTIPLE_HOLDERS", "ONE_USER_ALL_DEVICES", "ONE_USER_ONE_DEVICE", "multipleHolders", "oneUserAllDevices", "oneUserOneDevice"], "enumDeprecated": [false, false, false, false, true, true, true], "enumDescriptions": ["Unspecified preference.", "The Pass object is shareable by a user and can be saved by any number of different users, and on any number of devices. Partners typically use this setup for passes that do not need to be restricted to a single user or pinned to a single device.", "An object can only be saved by one user, but this user can view and use it on multiple of their devices. Once the first user saves the object, no other user will be allowed to view or save it.", "An object can only be saved by one user on a single device. Intended for use by select partners in limited circumstances. An example use case is a transit ticket that should be \"device pinned\", meaning it can be saved, viewed and used only by a single user on a single device. Contact support for additional information.", "Legacy alias for `MULTIP<PERSON>_HOLDERS`. Deprecated.", "Legacy alias for `ONE_USER_ALL_DEVICES`. Deprecated.", "Legacy alias for `ONE_USER_ONE_DEVICE`. Deprecated."], "type": "string"}, "redemptionIssuers": {"description": "Identifies which redemption issuers can redeem the pass over Smart Tap. Redemption issuers are identified by their issuer ID. Redemption issuers must have at least one Smart Tap key configured. The `enableSmartTap` and object level `smartTapRedemptionLevel` fields must also be set up correctly in order for a pass to support Smart Tap.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "securityAnimation": {"$ref": "SecurityAnimation", "description": "Optional information about the security animation. If this is set a security animation will be rendered on pass details."}, "textModulesData": {"description": "Text module data. If `textModulesData` is also defined on the object, both will be displayed. The maximum number of these fields displayed is 10 from class and 10 from object.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the class. For a pass only ten will be displayed, prioritizing those from the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "viewUnlockRequirement": {"description": "View Unlock Requirement options for the generic pass.", "enum": ["VIEW_UNLOCK_REQUIREMENT_UNSPECIFIED", "UNLOCK_NOT_REQUIRED", "UNLOCK_REQUIRED_TO_VIEW"], "enumDescriptions": ["Default value, same as UNLOCK_NOT_REQUIRED.", "Default behavior for all the existing Passes if ViewUnlockRequirement is not set.", "Requires the user to unlock their device each time the pass is viewed. If the user removes their device lock after saving the pass, then they will be prompted to create a device lock before the pass can be viewed."], "type": "string"}}, "type": "object"}, "GenericClassAddMessageResponse": {"description": "Response to adding a new issuer message to the class. This contains the entire updated GenericClass.", "id": "GenericClassAddMessageResponse", "properties": {"resource": {"$ref": "GenericClass", "description": "The updated EventTicketClass resource."}}, "type": "object"}, "GenericClassListResponse": {"description": "List response which contains the list of all generic classes for a given issuer ID.", "id": "GenericClassListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "GenericClass"}, "type": "array"}}, "type": "object"}, "GenericObject": {"description": "Generic Object", "id": "GenericObject", "properties": {"appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding class only object AppLinkData will be displayed."}, "barcode": {"$ref": "Barcode", "description": "The barcode type and value. If pass does not have a barcode, we can allow the issuer to set Barcode.alternate_text and display just that."}, "cardTitle": {"$ref": "LocalizedString", "description": "Required. The header of the pass. This is usually the Business name such as \"XXX Gym\", \"AAA Insurance\". This field is required and appears in the header row at the very top of the pass."}, "classId": {"description": "Required. The class associated with this object. The class must be of the same type as this object, must already exist, and must be approved. Class IDs should follow the format `issuerID.identifier` where `issuerID` is issued by Google and `identifier` is chosen by you.", "type": "string"}, "genericType": {"description": "Specify which `GenericType` the card belongs to.", "enum": ["GENERIC_TYPE_UNSPECIFIED", "GENERIC_SEASON_PASS", "GENERIC_UTILITY_BILLS", "GENERIC_PARKING_PASS", "GENERIC_VOUCHER", "GENERIC_GYM_MEMBERSHIP", "GENERIC_LIBRARY_MEMBERSHIP", "GENERIC_RESERVATIONS", "GENERIC_AUTO_INSURANCE", "GENERIC_HOME_INSURANCE", "GENERIC_ENTRY_TICKET", "GENERIC_RECEIPT", "GENERIC_LOYALTY_CARD", "GENERIC_OTHER"], "enumDescriptions": ["Unspecified generic type.", "Season pass", "Utility bills", "Parking pass", "Voucher", "Gym membership cards", "Library membership cards", "Reservations", "Auto-insurance cards", "Home-insurance cards", "Entry tickets", "Receipts", "Loyalty cards. Please note that it is advisable to use a dedicated Loyalty card pass type instead of this generic type. A dedicated loyalty card pass type offers more features and functionality than a generic pass type.", "Other type"], "type": "string"}, "groupingInfo": {"$ref": "GroupingInfo", "description": "Information that controls how passes are grouped together."}, "hasUsers": {"description": "Indicates if the object has users. This field is set by the platform.", "type": "boolean"}, "header": {"$ref": "LocalizedString", "description": "Required. The title of the pass, such as \"50% off coupon\" or \"Library card\" or \"Voucher\". This field is required and appears in the title row of the pass detail view."}, "heroImage": {"$ref": "Image", "description": "Banner image displayed on the front of the card if present. The image will be displayed at 100% width."}, "hexBackgroundColor": {"description": "The background color for the card. If not set, the dominant color of the hero image is used, and if no hero image is set, the dominant color of the logo is used and if logo is not set, a color would be chosen by Google.", "type": "string"}, "id": {"description": "Required. The unique identifier for an object. This ID must be unique across all objects from an issuer. This value needs to follow the format `issuerID.identifier` where `issuerID` is issued by Google and `identifier` is chosen by you. The unique identifier can only include alphanumeric characters, `.`, `_`, or `-`.", "type": "string"}, "imageModulesData": {"description": "Image module data. Only one of the image from class and one from object level will be rendered when both set.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "linkedObjectIds": {"description": "linked_object_ids are a list of other objects such as event ticket, loyalty, offer, generic, giftcard, transit and boarding pass that should be automatically attached to this generic object. If a user had saved this generic card, then these linked_object_ids would be automatically pushed to the user's wallet (unless they turned off the setting to receive such linked passes). Make sure that objects present in linked_object_ids are already inserted - if not, calls would fail. Once linked, the linked objects cannot be unlinked. You cannot link objects belonging to another issuer. There is a limit to the number of objects that can be linked to a single object. After the limit is reached, new linked objects in the call will be ignored silently. Object IDs should follow the format issuer ID. identifier where the former is issued by Google and the latter is chosen by you.", "items": {"type": "string"}, "type": "array"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If `linksModuleData` is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from class and 10 from object."}, "logo": {"$ref": "Image", "description": "The logo image of the pass. This image is displayed in the card detail view in upper left, and also on the list/thumbnail view. If the logo is not present, the first letter of `cardTitle` would be shown as logo."}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the object. Any additional MerchantLocations added beyond the 10 will be rejected. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "notifications": {"$ref": "Notifications", "description": "The notification settings that are enabled for this object."}, "passConstraints": {"$ref": "PassConstraints", "description": "Pass constraints for the object. Includes limiting NFC and screenshot behaviors."}, "rotatingBarcode": {"$ref": "RotatingBarcode", "description": "The rotating barcode settings/details."}, "saveRestrictions": {"$ref": "SaveRestrictions", "description": "Restrictions on the object that needs to be verified before the user tries to save the pass. Note that this restrictions will only be applied during save time. If the restrictions changed after a user saves the pass, the new restrictions will not be applied to an already saved pass."}, "smartTapRedemptionValue": {"description": "The value that will be transmitted to a Smart Tap certified terminal over NFC for this object. The class level fields `enableSmartTap` and `redemptionIssuers` must also be set up correctly in order for the pass to support Smart Tap. Only ASCII characters are supported.", "type": "string"}, "state": {"description": "The state of the object. This field is used to determine how an object is displayed in the app. For example, an `inactive` object is moved to the \"Expired passes\" section. If this is not provided, the object would be considered `ACTIVE`.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "active", "COMPLETED", "completed", "EXPIRED", "expired", "INACTIVE", "inactive"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["Default value.", "Object is active and displayed to with other active objects.", "Legacy alias for `ACTIVE`. Deprecated.", "Object has completed it's lifecycle.", "Legacy alias for `COMPLETED`. Deprecated.", "Object is no longer valid (`validTimeInterval` passed).", "Legacy alias for `EXPIRED`. Deprecated.", "Object is no longer valid", "Legacy alias for `INACTIVE`. Deprecated."], "type": "string"}, "subheader": {"$ref": "LocalizedString", "description": "The title label of the pass, such as location where this pass can be used. Appears right above the title in the title row in the pass detail view."}, "textModulesData": {"description": "Text module data. If `textModulesData` is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from class and 10 from object.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "validTimeInterval": {"$ref": "TimeInterval", "description": "The time period this object will be considered valid or usable. When the time period is passed, the object will be considered expired, which will affect the rendering on user's devices."}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "wideLogo": {"$ref": "Image", "description": "The wide logo of the pass. When provided, this will be used in place of the logo in the top left of the card view."}}, "type": "object"}, "GenericObjectAddMessageResponse": {"description": "Response to adding a new issuer message to the object. This contains the entire updated GenericObject.", "id": "GenericObjectAddMessageResponse", "properties": {"resource": {"$ref": "GenericObject", "description": "The updated GenericObject resource."}}, "type": "object"}, "GenericObjectListResponse": {"description": "List response which contains the list of all generic objects for a given issuer ID.", "id": "GenericObjectListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "GenericObject"}, "type": "array"}}, "type": "object"}, "GiftCardClass": {"id": "GiftCardClass", "properties": {"allowBarcodeRedemption": {"description": "Determines whether the merchant supports gift card redemption using barcode. If true, app displays a barcode for the gift card on the Gift card details screen. If false, a barcode is not displayed.", "type": "boolean"}, "allowMultipleUsersPerObject": {"deprecated": true, "description": "Deprecated. Use `multipleDevicesAndHoldersAllowedStatus` instead.", "type": "boolean"}, "appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding object that will be used instead."}, "callbackOptions": {"$ref": "CallbackOptions", "description": "Callback options to be used to call the issuer back for every save/delete of an object for this class by the end-user. All objects of this class are eligible for the callback."}, "cardNumberLabel": {"description": "The label to display for the card number, such as \"Card Number\".", "type": "string"}, "classTemplateInfo": {"$ref": "ClassTemplateInfo", "description": "Template information about how the class should be displayed. If unset, Google will fallback to a default set of fields to display."}, "countryCode": {"description": "Country code used to display the card's country (when the user is not in that country), as well as to display localized content when content is not available in the user's locale.", "type": "string"}, "enableSmartTap": {"description": "Identifies whether this class supports Smart Tap. The `redemptionIssuers` and object level `smartTapRedemptionLevel` fields must also be set up correctly in order for a pass to support Smart Tap.", "type": "boolean"}, "eventNumberLabel": {"description": "The label to display for event number, such as \"Target Event #\".", "type": "string"}, "heroImage": {"$ref": "Image", "description": "Optional banner image displayed on the front of the card. If none is present, nothing will be displayed. The image will display at 100% width."}, "hexBackgroundColor": {"description": "The background color for the card. If not set the dominant color of the hero image is used, and if no hero image is set, the dominant color of the logo is used. The format is #rrggbb where rrggbb is a hex RGB triplet, such as `#ffcc00`. You can also use the shorthand version of the RGB triplet which is #rgb, such as `#fc0`.", "type": "string"}, "homepageUri": {"$ref": "<PERSON><PERSON>", "description": "The URI of your application's home page. Populating the URI in this field results in the exact same behavior as populating an URI in linksModuleData (when an object is rendered, a link to the homepage is shown in what would usually be thought of as the linksModuleData section of the object)."}, "id": {"description": "Required. The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "type": "string"}, "imageModulesData": {"description": "Image module data. The maximum number of these fields displayed is 1 from object level and 1 for class object level.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "infoModuleData": {"$ref": "InfoModuleData", "deprecated": true, "description": "Deprecated. Use textModulesData instead."}, "issuerName": {"description": "Required. The issuer name. Recommended maximum length is 20 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#giftCardClass\"`.", "type": "string"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If links module data is also defined on the object, both will be displayed."}, "localizedCardNumberLabel": {"$ref": "LocalizedString", "description": "Translated strings for the card_number_label."}, "localizedEventNumberLabel": {"$ref": "LocalizedString", "description": "Translated strings for the event_number_label."}, "localizedIssuerName": {"$ref": "LocalizedString", "description": "Translated strings for the issuer_name. Recommended maximum length is 20 characters to ensure full string is displayed on smaller screens."}, "localizedMerchantName": {"$ref": "LocalizedString", "description": "Translated strings for the merchant_name. The app may display an ellipsis after the first 20 characters to ensure full string is displayed on smaller screens."}, "localizedPinLabel": {"$ref": "LocalizedString", "description": "Translated strings for the pin_label."}, "locations": {"deprecated": true, "description": "Note: This field is currently not supported to trigger geo notifications.", "items": {"$ref": "LatLongPoint"}, "type": "array"}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the class. Any additional MerchantLocations added beyond the 10 will be rejected. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "merchantName": {"description": "Merchant name, such as \"Adam's Apparel\". The app may display an ellipsis after the first 20 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "multipleDevicesAndHoldersAllowedStatus": {"description": "Identifies whether multiple users and devices will save the same object referencing this class.", "enum": ["STATUS_UNSPECIFIED", "MULTIPLE_HOLDERS", "ONE_USER_ALL_DEVICES", "ONE_USER_ONE_DEVICE", "multipleHolders", "oneUserAllDevices", "oneUserOneDevice"], "enumDeprecated": [false, false, false, false, true, true, true], "enumDescriptions": ["Unspecified preference.", "The Pass object is shareable by a user and can be saved by any number of different users, and on any number of devices. Partners typically use this setup for passes that do not need to be restricted to a single user or pinned to a single device.", "An object can only be saved by one user, but this user can view and use it on multiple of their devices. Once the first user saves the object, no other user will be allowed to view or save it.", "An object can only be saved by one user on a single device. Intended for use by select partners in limited circumstances. An example use case is a transit ticket that should be \"device pinned\", meaning it can be saved, viewed and used only by a single user on a single device. Contact support for additional information.", "Legacy alias for `MULTIP<PERSON>_HOLDERS`. Deprecated.", "Legacy alias for `ONE_USER_ALL_DEVICES`. Deprecated.", "Legacy alias for `ONE_USER_ONE_DEVICE`. Deprecated."], "type": "string"}, "notifyPreference": {"description": "Whether or not field updates to this class should trigger notifications. When set to NOTIFY, we will attempt to trigger a field update notification to users. These notifications will only be sent to users if the field is part of an allowlist. If not specified, no notification will be triggered. This setting is ephemeral and needs to be set with each PATCH or UPDATE request, otherwise a notification will not be triggered.", "enum": ["NOTIFICATION_SETTINGS_FOR_UPDATES_UNSPECIFIED", "NOTIFY_ON_UPDATE"], "enumDescriptions": ["Default behavior is no notifications sent.", "This value will result in a notification being sent, if the updated fields are part of an allowlist."], "type": "string"}, "pinLabel": {"description": "The label to display for the PIN, such as \"4-digit PIN\".", "type": "string"}, "programLogo": {"$ref": "Image", "description": "The logo of the gift card program or company. This logo is displayed in both the details and list views of the app."}, "redemptionIssuers": {"description": "Identifies which redemption issuers can redeem the pass over Smart Tap. Redemption issuers are identified by their issuer ID. Redemption issuers must have at least one Smart Tap key configured. The `enableSmartTap` and object level `smartTapRedemptionLevel` fields must also be set up correctly in order for a pass to support Smart Tap.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "review": {"$ref": "Review", "description": "The review comments set by the platform when a class is marked `approved` or `rejected`."}, "reviewStatus": {"description": "Required. The status of the class. This field can be set to `draft` or `underReview` using the insert, patch, or update API calls. Once the review state is changed from `draft` it may not be changed back to `draft`. You should keep this field to `draft` when the class is under development. A `draft` class cannot be used to create any object. You should set this field to `underReview` when you believe the class is ready for use. The platform will automatically set this field to `approved` and it can be immediately used to create or migrate objects. When updating an already `approved` class you should keep setting this field to `underReview`.", "enum": ["REVIEW_STATUS_UNSPECIFIED", "UNDER_REVIEW", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "APPROVED", "approved", "REJECTED", "rejected", "DRAFT", "draft"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `UNDER_REVIEW`. Deprecated.", "", "Legacy alias for `APPROVED`. Deprecated.", "", "Legacy alias for `REJECTED`. Deprecated.", "", "Legacy alias for `DRAFT`. Deprecated."], "type": "string"}, "securityAnimation": {"$ref": "SecurityAnimation", "description": "Optional information about the security animation. If this is set a security animation will be rendered on pass details."}, "textModulesData": {"description": "Text module data. If text module data is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from the object and 10 from the class.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the class. For a pass only ten will be displayed, prioritizing those from the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "version": {"deprecated": true, "description": "Deprecated", "format": "int64", "type": "string"}, "viewUnlockRequirement": {"description": "View Unlock Requirement options for the gift card.", "enum": ["VIEW_UNLOCK_REQUIREMENT_UNSPECIFIED", "UNLOCK_NOT_REQUIRED", "UNLOCK_REQUIRED_TO_VIEW"], "enumDescriptions": ["Default value, same as UNLOCK_NOT_REQUIRED.", "Default behavior for all the existing Passes if ViewUnlockRequirement is not set.", "Requires the user to unlock their device each time the pass is viewed. If the user removes their device lock after saving the pass, then they will be prompted to create a device lock before the pass can be viewed."], "type": "string"}, "wideProgramLogo": {"$ref": "Image", "description": "The wide logo of the gift card program or company. When provided, this will be used in place of the program logo in the top left of the card view."}, "wordMark": {"$ref": "Image", "deprecated": true, "description": "Deprecated."}}, "type": "object"}, "GiftCardClassAddMessageResponse": {"id": "GiftCardClassAddMessageResponse", "properties": {"resource": {"$ref": "GiftCardClass", "description": "The updated GiftCardClass resource."}}, "type": "object"}, "GiftCardClassListResponse": {"id": "GiftCardClassListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "GiftCardClass"}, "type": "array"}}, "type": "object"}, "GiftCardObject": {"id": "GiftCardObject", "properties": {"appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding class only object AppLinkData will be displayed."}, "balance": {"$ref": "Money", "description": "The card's monetary balance."}, "balanceUpdateTime": {"$ref": "DateTime", "description": "The date and time when the balance was last updated. Offset is required. If balance is updated and this property is not provided, system will default to the current time."}, "barcode": {"$ref": "Barcode", "description": "The barcode type and value."}, "cardNumber": {"description": "Required. The card's number.", "type": "string"}, "classId": {"description": "Required. The class associated with this object. The class must be of the same type as this object, must already exist, and must be approved. Class IDs should follow the format issuer ID.identifier where the former is issued by Google and latter is chosen by you.", "type": "string"}, "classReference": {"$ref": "GiftCardClass", "description": "A copy of the inherited fields of the parent class. These fields are retrieved during a GET."}, "disableExpirationNotification": {"description": "Indicates if notifications should explicitly be suppressed. If this field is set to true, regardless of the `messages` field, expiration notifications to the user will be suppressed. By default, this field is set to false. Currently, this can only be set for offers.", "type": "boolean"}, "eventNumber": {"description": "The card's event number, an optional field used by some gift cards.", "type": "string"}, "groupingInfo": {"$ref": "GroupingInfo", "description": "Information that controls how passes are grouped together."}, "hasLinkedDevice": {"description": "Whether this object is currently linked to a single device. This field is set by the platform when a user saves the object, linking it to their device. Intended for use by select partners. Contact support for additional information.", "type": "boolean"}, "hasUsers": {"description": "Indicates if the object has users. This field is set by the platform.", "type": "boolean"}, "heroImage": {"$ref": "Image", "description": "Optional banner image displayed on the front of the card. If none is present, hero image of the class, if present, will be displayed. If hero image of the class is also not present, nothing will be displayed."}, "id": {"description": "Required. The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID.identifier where the former is issued by Google and latter is chosen by you. The unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "type": "string"}, "imageModulesData": {"description": "Image module data. The maximum number of these fields displayed is 1 from object level and 1 for class object level.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "infoModuleData": {"$ref": "InfoModuleData", "description": "Deprecated. Use textModulesData instead."}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#giftCardObject\"`.", "type": "string"}, "linkedObjectIds": {"description": "linked_object_ids are a list of other objects such as event ticket, loyalty, offer, generic, giftcard, transit and boarding pass that should be automatically attached to this giftcard object. If a user had saved this gift card, then these linked_object_ids would be automatically pushed to the user's wallet (unless they turned off the setting to receive such linked passes). Make sure that objects present in linked_object_ids are already inserted - if not, calls would fail. Once linked, the linked objects cannot be unlinked. You cannot link objects belonging to another issuer. There is a limit to the number of objects that can be linked to a single object. After the limit is reached, new linked objects in the call will be ignored silently. Object IDs should follow the format issuer ID. identifier where the former is issued by Google and the latter is chosen by you.", "items": {"type": "string"}, "type": "array"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If links module data is also defined on the class, both will be displayed."}, "locations": {"deprecated": true, "description": "Note: This field is currently not supported to trigger geo notifications.", "items": {"$ref": "LatLongPoint"}, "type": "array"}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the object. Any additional MerchantLocations added beyond the 10 will be rejected. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "notifyPreference": {"description": "Whether or not field updates to this object should trigger notifications. When set to NOTIFY, we will attempt to trigger a field update notification to users. These notifications will only be sent to users if the field is part of an allowlist. If set to DO_NOT_NOTIFY or NOTIFICATION_SETTINGS_UNSPECIFIED, no notification will be triggered. This setting is ephemeral and needs to be set with each PATCH or UPDATE request, otherwise a notification will not be triggered.", "enum": ["NOTIFICATION_SETTINGS_FOR_UPDATES_UNSPECIFIED", "NOTIFY_ON_UPDATE"], "enumDescriptions": ["Default behavior is no notifications sent.", "This value will result in a notification being sent, if the updated fields are part of an allowlist."], "type": "string"}, "passConstraints": {"$ref": "PassConstraints", "description": "Pass constraints for the object. Includes limiting NFC and screenshot behaviors."}, "pin": {"description": "The card's PIN.", "type": "string"}, "rotatingBarcode": {"$ref": "RotatingBarcode", "description": "The rotating barcode type and value."}, "saveRestrictions": {"$ref": "SaveRestrictions", "description": "Restrictions on the object that needs to be verified before the user tries to save the pass. Note that this restrictions will only be applied during save time. If the restrictions changed after a user saves the pass, the new restrictions will not be applied to an already saved pass."}, "smartTapRedemptionValue": {"description": "The value that will be transmitted to a Smart Tap certified terminal over NFC for this object. The class level fields `enableSmartTap` and `redemptionIssuers` must also be set up correctly in order for the pass to support Smart Tap. Only ASCII characters are supported.", "type": "string"}, "state": {"description": "Required. The state of the object. This field is used to determine how an object is displayed in the app. For example, an `inactive` object is moved to the \"Expired passes\" section.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "active", "COMPLETED", "completed", "EXPIRED", "expired", "INACTIVE", "inactive"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["Default value.", "Object is active and displayed to with other active objects.", "Legacy alias for `ACTIVE`. Deprecated.", "Object has completed it's lifecycle.", "Legacy alias for `COMPLETED`. Deprecated.", "Object is no longer valid (`validTimeInterval` passed).", "Legacy alias for `EXPIRED`. Deprecated.", "Object is no longer valid", "Legacy alias for `INACTIVE`. Deprecated."], "type": "string"}, "textModulesData": {"description": "Text module data. If text module data is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from the object and 10 from the class.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "validTimeInterval": {"$ref": "TimeInterval", "description": "The time period this object will be `active` and object can be used. An object's state will be changed to `expired` when this time period has passed."}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "version": {"deprecated": true, "description": "Deprecated", "format": "int64", "type": "string"}}, "type": "object"}, "GiftCardObjectAddMessageResponse": {"id": "GiftCardObjectAddMessageResponse", "properties": {"resource": {"$ref": "GiftCardObject", "description": "The updated GiftCardObject resource."}}, "type": "object"}, "GiftCardObjectListResponse": {"id": "GiftCardObjectListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "GiftCardObject"}, "type": "array"}}, "type": "object"}, "GroupingInfo": {"id": "GroupingInfo", "properties": {"groupingId": {"description": "Optional grouping ID for grouping the passes with the same ID visually together. Grouping with different types of passes is allowed.", "type": "string"}, "sortIndex": {"description": "Optional index for sorting the passes when they are grouped with other passes. Passes with lower sort index are shown before passes with higher sort index. If unspecified, the value is assumed to be INT_MAX. For two passes with the same sort index, the sorting behavior is undefined.", "format": "int32", "type": "integer"}}, "type": "object"}, "Image": {"description": "Wrapping type for Google hosted images.", "id": "Image", "properties": {"contentDescription": {"$ref": "LocalizedString", "description": "Description of the image used for accessibility."}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#image\"`.", "type": "string"}, "sourceUri": {"$ref": "ImageUri", "description": "The URI for the image."}}, "type": "object"}, "ImageModuleData": {"id": "ImageModuleData", "properties": {"id": {"description": "The ID associated with an image module. This field is here to enable ease of management of image modules.", "type": "string"}, "mainImage": {"$ref": "Image", "description": "A 100% width image."}}, "type": "object"}, "ImageUri": {"id": "ImageUri", "properties": {"description": {"deprecated": true, "description": "Additional information about the image, which is unused and retained only for backward compatibility.", "type": "string"}, "localizedDescription": {"$ref": "LocalizedString", "deprecated": true, "description": "Translated strings for the description, which are unused and retained only for backward compatibility."}, "uri": {"description": "The location of the image. URIs must have a scheme.", "type": "string"}}, "type": "object"}, "InfoModuleData": {"id": "InfoModuleData", "properties": {"labelValueRows": {"description": "A list of collections of labels and values. These will be displayed one after the other in a singular column.", "items": {"$ref": "LabelValueRow"}, "type": "array"}, "showLastUpdateTime": {"deprecated": true, "type": "boolean"}}, "type": "object"}, "Issuer": {"id": "Issuer", "properties": {"callbackOptions": {"$ref": "CallbackOptions", "description": "Allows the issuer to provide their callback settings."}, "contactInfo": {"$ref": "IssuerContactInfo", "description": "Issuer contact information."}, "homepageUrl": {"description": "URL for the issuer's home page.", "type": "string"}, "issuerId": {"description": "The unique identifier for an issuer account. This is automatically generated when the issuer is inserted.", "format": "int64", "type": "string"}, "name": {"description": "The account name of the issuer.", "type": "string"}, "smartTapMerchantData": {"$ref": "SmartTapMerchantData", "description": "Available only to Smart Tap enabled partners. Contact support for additional guidance."}}, "type": "object"}, "IssuerContactInfo": {"id": "IssuerContactInfo", "properties": {"alertsEmails": {"description": "Email addresses which will receive alerts.", "items": {"type": "string"}, "type": "array"}, "email": {"description": "The primary contact email address.", "type": "string"}, "name": {"description": "The primary contact name.", "type": "string"}, "phone": {"description": "The primary contact phone number.", "type": "string"}}, "type": "object"}, "IssuerListResponse": {"id": "IssuerListResponse", "properties": {"resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "Issuer"}, "type": "array"}}, "type": "object"}, "IssuerToUserInfo": {"id": "IssuerToUserInfo", "properties": {"action": {"enum": ["ACTION_UNSPECIFIED", "S2AP", "s2ap", "SIGN_UP", "signUp"], "enumDeprecated": [false, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `S2AP`. Deprecated.", "", "Legacy alias for `SIGN_UP`. Deprecated."], "type": "string"}, "signUpInfo": {"$ref": "SignUpInfo"}, "url": {"description": "Currently not used, consider deprecating.", "type": "string"}, "value": {"description": "JSON web token for action S2AP.", "type": "string"}}, "type": "object"}, "JwtInsertResponse": {"id": "JwtInsertResponse", "properties": {"resources": {"$ref": "Resources", "description": "Data that corresponds to the ids of the provided classes and objects in the JWT. resources will only include the non-empty arrays (i.e. if the JWT only includes eventTicketObjects, then that is the only field that will be present in resources)."}, "saveUri": {"description": "A URI that, when opened, will allow the end user to save the object(s) identified in the JWT to their Google account.", "type": "string"}}, "type": "object"}, "JwtResource": {"id": "JwtResource", "properties": {"jwt": {"description": "A string representing a JWT of the format described at https://developers.google.com/wallet/reference/rest/v1/Jwt", "type": "string"}}, "type": "object"}, "LabelValue": {"description": "A pair of text strings to be displayed in the details view. Note we no longer display LabelValue/LabelValueRow as a table, instead a list of items.", "id": "LabelValue", "properties": {"label": {"description": "The label for a specific row and column. Recommended maximum is 15 characters for a two-column layout and 30 characters for a one-column layout.", "type": "string"}, "localizedLabel": {"$ref": "LocalizedString", "description": "Translated strings for the label. Recommended maximum is 15 characters for a two-column layout and 30 characters for a one-column layout."}, "localizedValue": {"$ref": "LocalizedString", "description": "Translated strings for the value. Recommended maximum is 15 characters for a two-column layout and 30 characters for a one-column layout."}, "value": {"description": "The value for a specific row and column. Recommended maximum is 15 characters for a two-column layout and 30 characters for a one-column layout.", "type": "string"}}, "type": "object"}, "LabelValueRow": {"id": "LabelValueRow", "properties": {"columns": {"description": "A list of labels and values. These will be displayed in a singular column, one after the other, not in multiple columns, despite the field name.", "items": {"$ref": "LabelValue"}, "type": "array"}}, "type": "object"}, "LatLongPoint": {"id": "LatLongPoint", "properties": {"kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#latLongPoint\"`.", "type": "string"}, "latitude": {"description": "The latitude specified as any value in the range of -90.0 through +90.0, both inclusive. Values outside these bounds will be rejected.", "format": "double", "type": "number"}, "longitude": {"description": "The longitude specified in the range -180.0 through +180.0, both inclusive. Values outside these bounds will be rejected.", "format": "double", "type": "number"}}, "type": "object"}, "LinksModuleData": {"id": "LinksModuleData", "properties": {"uris": {"description": "The list of URIs.", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "ListTemplateOverride": {"id": "ListTemplateOverride", "properties": {"firstRowOption": {"$ref": "FirstRowOption", "description": "Specifies from a predefined set of options or from a reference to the field what will be displayed in the first row. To set this override, set the FirstRowOption.fieldOption to the FieldSelector of your choice."}, "secondRowOption": {"$ref": "FieldSelector", "description": "A reference to the field to be displayed in the second row. This option is only displayed if there are not multiple user objects in a group. If there is a group, the second row will always display a field shared by all objects. To set this override, please set secondRowOption to the FieldSelector of you choice."}, "thirdRowOption": {"$ref": "FieldSelector", "deprecated": true, "description": "An unused/deprecated field. Setting it will have no effect on what the user sees."}}, "type": "object"}, "LocalizedString": {"id": "LocalizedString", "properties": {"defaultValue": {"$ref": "TranslatedString", "description": "Contains the string to be displayed if no appropriate translation is available."}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#localizedString\"`.", "type": "string"}, "translatedValues": {"description": "Contains the translations for the string.", "items": {"$ref": "TranslatedString"}, "type": "array"}}, "type": "object"}, "LoyaltyClass": {"id": "LoyaltyClass", "properties": {"accountIdLabel": {"description": "The account ID label, such as \"Member ID.\" Recommended maximum length is 15 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "accountNameLabel": {"description": "The account name label, such as \"Member Name.\" Recommended maximum length is 15 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "allowMultipleUsersPerObject": {"deprecated": true, "description": "Deprecated. Use `multipleDevicesAndHoldersAllowedStatus` instead.", "type": "boolean"}, "appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding object that will be used instead."}, "callbackOptions": {"$ref": "CallbackOptions", "description": "Callback options to be used to call the issuer back for every save/delete of an object for this class by the end-user. All objects of this class are eligible for the callback."}, "classTemplateInfo": {"$ref": "ClassTemplateInfo", "description": "Template information about how the class should be displayed. If unset, Google will fallback to a default set of fields to display."}, "countryCode": {"description": "Country code used to display the card's country (when the user is not in that country), as well as to display localized content when content is not available in the user's locale.", "type": "string"}, "discoverableProgram": {"$ref": "DiscoverableProgram", "description": "Information about how the class may be discovered and instantiated from within the Google Pay app."}, "enableSmartTap": {"description": "Identifies whether this class supports Smart Tap. The `redemptionIssuers` and one of object level `smartTapRedemptionLevel`, barcode.value`, or `accountId` fields must also be set up correctly in order for a pass to support Smart Tap.", "type": "boolean"}, "heroImage": {"$ref": "Image", "description": "Optional banner image displayed on the front of the card. If none is present, nothing will be displayed. The image will display at 100% width."}, "hexBackgroundColor": {"description": "The background color for the card. If not set the dominant color of the hero image is used, and if no hero image is set, the dominant color of the logo is used. The format is #rrggbb where rrggbb is a hex RGB triplet, such as `#ffcc00`. You can also use the shorthand version of the RGB triplet which is #rgb, such as `#fc0`.", "type": "string"}, "homepageUri": {"$ref": "<PERSON><PERSON>", "description": "The URI of your application's home page. Populating the URI in this field results in the exact same behavior as populating an URI in linksModuleData (when an object is rendered, a link to the homepage is shown in what would usually be thought of as the linksModuleData section of the object)."}, "id": {"description": "Required. The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "type": "string"}, "imageModulesData": {"description": "Image module data. The maximum number of these fields displayed is 1 from object level and 1 for class object level.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "infoModuleData": {"$ref": "InfoModuleData", "deprecated": true, "description": "Deprecated. Use textModulesData instead."}, "issuerName": {"description": "Required. The issuer name. Recommended maximum length is 20 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#loyaltyClass\"`.", "type": "string"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If links module data is also defined on the object, both will be displayed."}, "localizedAccountIdLabel": {"$ref": "LocalizedString", "description": "Translated strings for the account_id_label. Recommended maximum length is 15 characters to ensure full string is displayed on smaller screens."}, "localizedAccountNameLabel": {"$ref": "LocalizedString", "description": "Translated strings for the account_name_label. Recommended maximum length is 15 characters to ensure full string is displayed on smaller screens."}, "localizedIssuerName": {"$ref": "LocalizedString", "description": "Translated strings for the issuer_name. Recommended maximum length is 20 characters to ensure full string is displayed on smaller screens."}, "localizedProgramName": {"$ref": "LocalizedString", "description": "Translated strings for the program_name. The app may display an ellipsis after the first 20 characters to ensure full string is displayed on smaller screens."}, "localizedRewardsTier": {"$ref": "LocalizedString", "description": "Translated strings for the rewards_tier. Recommended maximum length is 7 characters to ensure full string is displayed on smaller screens."}, "localizedRewardsTierLabel": {"$ref": "LocalizedString", "description": "Translated strings for the rewards_tier_label. Recommended maximum length is 9 characters to ensure full string is displayed on smaller screens."}, "localizedSecondaryRewardsTier": {"$ref": "LocalizedString", "description": "Translated strings for the secondary_rewards_tier."}, "localizedSecondaryRewardsTierLabel": {"$ref": "LocalizedString", "description": "Translated strings for the secondary_rewards_tier_label."}, "locations": {"deprecated": true, "description": "Note: This field is currently not supported to trigger geo notifications.", "items": {"$ref": "LatLongPoint"}, "type": "array"}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the class. Any additional MerchantLocations added beyond the 10 will be rejected. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "multipleDevicesAndHoldersAllowedStatus": {"description": "Identifies whether multiple users and devices will save the same object referencing this class.", "enum": ["STATUS_UNSPECIFIED", "MULTIPLE_HOLDERS", "ONE_USER_ALL_DEVICES", "ONE_USER_ONE_DEVICE", "multipleHolders", "oneUserAllDevices", "oneUserOneDevice"], "enumDeprecated": [false, false, false, false, true, true, true], "enumDescriptions": ["Unspecified preference.", "The Pass object is shareable by a user and can be saved by any number of different users, and on any number of devices. Partners typically use this setup for passes that do not need to be restricted to a single user or pinned to a single device.", "An object can only be saved by one user, but this user can view and use it on multiple of their devices. Once the first user saves the object, no other user will be allowed to view or save it.", "An object can only be saved by one user on a single device. Intended for use by select partners in limited circumstances. An example use case is a transit ticket that should be \"device pinned\", meaning it can be saved, viewed and used only by a single user on a single device. Contact support for additional information.", "Legacy alias for `MULTIP<PERSON>_HOLDERS`. Deprecated.", "Legacy alias for `ONE_USER_ALL_DEVICES`. Deprecated.", "Legacy alias for `ONE_USER_ONE_DEVICE`. Deprecated."], "type": "string"}, "notifyPreference": {"description": "Whether or not field updates to this class should trigger notifications. When set to NOTIFY, we will attempt to trigger a field update notification to users. These notifications will only be sent to users if the field is part of an allowlist. If not specified, no notification will be triggered. This setting is ephemeral and needs to be set with each PATCH or UPDATE request, otherwise a notification will not be triggered.", "enum": ["NOTIFICATION_SETTINGS_FOR_UPDATES_UNSPECIFIED", "NOTIFY_ON_UPDATE"], "enumDescriptions": ["Default behavior is no notifications sent.", "This value will result in a notification being sent, if the updated fields are part of an allowlist."], "type": "string"}, "programLogo": {"$ref": "Image", "description": "Required. The logo of the loyalty program or company. This logo is displayed in both the details and list views of the app."}, "programName": {"description": "Required. The program name, such as \"Adam's Apparel\". The app may display an ellipsis after the first 20 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "redemptionIssuers": {"description": "Identifies which redemption issuers can redeem the pass over Smart Tap. Redemption issuers are identified by their issuer ID. Redemption issuers must have at least one Smart Tap key configured. The `enableSmartTap` and one of object level `smartTapRedemptionValue`, barcode.value`, or `accountId` fields must also be set up correctly in order for a pass to support Smart Tap.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "review": {"$ref": "Review", "description": "The review comments set by the platform when a class is marked `approved` or `rejected`."}, "reviewStatus": {"description": "Required. The status of the class. This field can be set to `draft` or `underReview` using the insert, patch, or update API calls. Once the review state is changed from `draft` it may not be changed back to `draft`. You should keep this field to `draft` when the class is under development. A `draft` class cannot be used to create any object. You should set this field to `underReview` when you believe the class is ready for use. The platform will automatically set this field to `approved` and it can be immediately used to create or migrate objects. When updating an already `approved` class you should keep setting this field to `underReview`.", "enum": ["REVIEW_STATUS_UNSPECIFIED", "UNDER_REVIEW", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "APPROVED", "approved", "REJECTED", "rejected", "DRAFT", "draft"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `UNDER_REVIEW`. Deprecated.", "", "Legacy alias for `APPROVED`. Deprecated.", "", "Legacy alias for `REJECTED`. Deprecated.", "", "Legacy alias for `DRAFT`. Deprecated."], "type": "string"}, "rewardsTier": {"description": "The rewards tier, such as \"Gold\" or \"Platinum.\" Recommended maximum length is 7 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "rewardsTierLabel": {"description": "The rewards tier label, such as \"Rewards Tier.\" Recommended maximum length is 9 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "secondaryRewardsTier": {"description": "The secondary rewards tier, such as \"Gold\" or \"Platinum.\"", "type": "string"}, "secondaryRewardsTierLabel": {"description": "The secondary rewards tier label, such as \"Rewards Tier.\"", "type": "string"}, "securityAnimation": {"$ref": "SecurityAnimation", "description": "Optional information about the security animation. If this is set a security animation will be rendered on pass details."}, "textModulesData": {"description": "Text module data. If text module data is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from the object and 10 from the class.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the class. For a pass only ten will be displayed, prioritizing those from the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "version": {"deprecated": true, "description": "Deprecated", "format": "int64", "type": "string"}, "viewUnlockRequirement": {"description": "View Unlock Requirement options for the loyalty card.", "enum": ["VIEW_UNLOCK_REQUIREMENT_UNSPECIFIED", "UNLOCK_NOT_REQUIRED", "UNLOCK_REQUIRED_TO_VIEW"], "enumDescriptions": ["Default value, same as UNLOCK_NOT_REQUIRED.", "Default behavior for all the existing Passes if ViewUnlockRequirement is not set.", "Requires the user to unlock their device each time the pass is viewed. If the user removes their device lock after saving the pass, then they will be prompted to create a device lock before the pass can be viewed."], "type": "string"}, "wideProgramLogo": {"$ref": "Image", "description": "The wide logo of the loyalty program or company. When provided, this will be used in place of the program logo in the top left of the card view."}, "wordMark": {"$ref": "Image", "deprecated": true, "description": "Deprecated."}}, "type": "object"}, "LoyaltyClassAddMessageResponse": {"id": "LoyaltyClassAddMessageResponse", "properties": {"resource": {"$ref": "LoyaltyClass", "description": "The updated LoyaltyClass resource."}}, "type": "object"}, "LoyaltyClassListResponse": {"id": "LoyaltyClassListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "LoyaltyClass"}, "type": "array"}}, "type": "object"}, "LoyaltyObject": {"id": "LoyaltyObject", "properties": {"accountId": {"description": "The loyalty account identifier. Recommended maximum length is 20 characters.", "type": "string"}, "accountName": {"description": "The loyalty account holder name, such as \"<PERSON>.\" Recommended maximum length is 20 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding class only object AppLinkData will be displayed."}, "barcode": {"$ref": "Barcode", "description": "The barcode type and value."}, "classId": {"description": "Required. The class associated with this object. The class must be of the same type as this object, must already exist, and must be approved. Class IDs should follow the format issuer ID.identifier where the former is issued by Google and latter is chosen by you.", "type": "string"}, "classReference": {"$ref": "LoyaltyClass", "description": "A copy of the inherited fields of the parent class. These fields are retrieved during a GET."}, "disableExpirationNotification": {"description": "Indicates if notifications should explicitly be suppressed. If this field is set to true, regardless of the `messages` field, expiration notifications to the user will be suppressed. By default, this field is set to false. Currently, this can only be set for offers.", "type": "boolean"}, "groupingInfo": {"$ref": "GroupingInfo", "description": "Information that controls how passes are grouped together."}, "hasLinkedDevice": {"description": "Whether this object is currently linked to a single device. This field is set by the platform when a user saves the object, linking it to their device. Intended for use by select partners. Contact support for additional information.", "type": "boolean"}, "hasUsers": {"description": "Indicates if the object has users. This field is set by the platform.", "type": "boolean"}, "heroImage": {"$ref": "Image", "description": "Optional banner image displayed on the front of the card. If none is present, hero image of the class, if present, will be displayed. If hero image of the class is also not present, nothing will be displayed."}, "id": {"description": "Required. The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID.identifier where the former is issued by Google and latter is chosen by you. The unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "type": "string"}, "imageModulesData": {"description": "Image module data. The maximum number of these fields displayed is 1 from object level and 1 for class object level.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "infoModuleData": {"$ref": "InfoModuleData", "description": "Deprecated. Use textModulesData instead."}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#loyaltyObject\"`.", "type": "string"}, "linkedObjectIds": {"description": "linked_object_ids are a list of other objects such as event ticket, loyalty, offer, generic, giftcard, transit and boarding pass that should be automatically attached to this loyalty object. If a user had saved this loyalty card, then these linked_object_ids would be automatically pushed to the user's wallet (unless they turned off the setting to receive such linked passes). Make sure that objects present in linked_object_ids are already inserted - if not, calls would fail. Once linked, the linked objects cannot be unlinked. You cannot link objects belonging to another issuer. There is a limit to the number of objects that can be linked to a single object. After the limit is reached, new linked objects in the call will be ignored silently. Object IDs should follow the format issuer ID. identifier where the former is issued by Google and the latter is chosen by you.", "items": {"type": "string"}, "type": "array"}, "linkedOfferIds": {"description": "A list of offer objects linked to this loyalty card. The offer objects must already exist. Offer object IDs should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you.", "items": {"type": "string"}, "type": "array"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If links module data is also defined on the class, both will be displayed."}, "locations": {"deprecated": true, "description": "Note: This field is currently not supported to trigger geo notifications.", "items": {"$ref": "LatLongPoint"}, "type": "array"}, "loyaltyPoints": {"$ref": "LoyaltyPoints", "description": "The loyalty reward points label, balance, and type."}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the object. Any additional MerchantLocations added beyond the 10 will be rejected. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "notifyPreference": {"description": "Whether or not field updates to this object should trigger notifications. When set to NOTIFY, we will attempt to trigger a field update notification to users. These notifications will only be sent to users if the field is part of an allowlist. If set to DO_NOT_NOTIFY or NOTIFICATION_SETTINGS_UNSPECIFIED, no notification will be triggered. This setting is ephemeral and needs to be set with each PATCH or UPDATE request, otherwise a notification will not be triggered.", "enum": ["NOTIFICATION_SETTINGS_FOR_UPDATES_UNSPECIFIED", "NOTIFY_ON_UPDATE"], "enumDescriptions": ["Default behavior is no notifications sent.", "This value will result in a notification being sent, if the updated fields are part of an allowlist."], "type": "string"}, "passConstraints": {"$ref": "PassConstraints", "description": "Pass constraints for the object. Includes limiting NFC and screenshot behaviors."}, "rotatingBarcode": {"$ref": "RotatingBarcode", "description": "The rotating barcode type and value."}, "saveRestrictions": {"$ref": "SaveRestrictions", "description": "Restrictions on the object that needs to be verified before the user tries to save the pass. Note that this restrictions will only be applied during save time. If the restrictions changed after a user saves the pass, the new restrictions will not be applied to an already saved pass."}, "secondaryLoyaltyPoints": {"$ref": "LoyaltyPoints", "description": "The secondary loyalty reward points label, balance, and type. Shown in addition to the primary loyalty points."}, "smartTapRedemptionValue": {"description": "The value that will be transmitted to a Smart Tap certified terminal over NFC for this object. The class level fields `enableSmartTap` and `redemptionIssuers` must also be set up correctly in order for the pass to support Smart Tap. Only ASCII characters are supported. If this value is not set but the class level fields `enableSmartTap` and `redemptionIssuers` are set up correctly, the `barcode.value` or the `accountId` fields are used as fallback if present.", "type": "string"}, "state": {"description": "Required. The state of the object. This field is used to determine how an object is displayed in the app. For example, an `inactive` object is moved to the \"Expired passes\" section.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "active", "COMPLETED", "completed", "EXPIRED", "expired", "INACTIVE", "inactive"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["Default value.", "Object is active and displayed to with other active objects.", "Legacy alias for `ACTIVE`. Deprecated.", "Object has completed it's lifecycle.", "Legacy alias for `COMPLETED`. Deprecated.", "Object is no longer valid (`validTimeInterval` passed).", "Legacy alias for `EXPIRED`. Deprecated.", "Object is no longer valid", "Legacy alias for `INACTIVE`. Deprecated."], "type": "string"}, "textModulesData": {"description": "Text module data. If text module data is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from the object and 10 from the class.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "validTimeInterval": {"$ref": "TimeInterval", "description": "The time period this object will be `active` and object can be used. An object's state will be changed to `expired` when this time period has passed."}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "version": {"deprecated": true, "description": "Deprecated", "format": "int64", "type": "string"}}, "type": "object"}, "LoyaltyObjectAddMessageResponse": {"id": "LoyaltyObjectAddMessageResponse", "properties": {"resource": {"$ref": "LoyaltyObject", "description": "The updated LoyaltyObject resource."}}, "type": "object"}, "LoyaltyObjectListResponse": {"id": "LoyaltyObjectListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "LoyaltyObject"}, "type": "array"}}, "type": "object"}, "LoyaltyPoints": {"id": "LoyaltyPoints", "properties": {"balance": {"$ref": "LoyaltyPointsBalance", "description": "The account holder's loyalty point balance, such as \"500\" or \"$10.00\". Recommended maximum length is 7 characters. This is a required field of `loyaltyPoints` and `secondaryLoyaltyPoints`."}, "label": {"description": "The loyalty points label, such as \"Points\". Recommended maximum length is 9 characters.", "type": "string"}, "localizedLabel": {"$ref": "LocalizedString", "description": "Translated strings for the label. Recommended maximum length is 9 characters."}}, "type": "object"}, "LoyaltyPointsBalance": {"id": "LoyaltyPointsBalance", "properties": {"double": {"description": "The double form of a balance. Only one of these subtypes (string, int, double, money) should be populated.", "format": "double", "type": "number"}, "int": {"description": "The integer form of a balance. Only one of these subtypes (string, int, double, money) should be populated.", "format": "int32", "type": "integer"}, "money": {"$ref": "Money", "description": "The money form of a balance. Only one of these subtypes (string, int, double, money) should be populated."}, "string": {"description": "The string form of a balance. Only one of these subtypes (string, int, double, money) should be populated.", "type": "string"}}, "type": "object"}, "Media": {"description": "A reference to data stored on the filesystem, on GFS or in blobstore.", "id": "Media", "properties": {"algorithm": {"deprecated": true, "description": "Deprecated, use one of explicit hash type fields instead. Algorithm used for calculating the hash. As of 2011/01/21, \"MD5\" is the only possible value for this field. New values may be added at any time.", "type": "string"}, "bigstoreObjectRef": {"deprecated": true, "description": "Use object_id instead.", "format": "byte", "type": "string"}, "blobRef": {"deprecated": true, "description": "Blobstore v1 reference, set if reference_type is BLOBSTORE_REF This should be the byte representation of a blobstore.BlobRef. Since Blobstore is deprecating v1, use blobstore2_info instead. For now, any v2 blob will also be represented in this field as v1 BlobRef.", "format": "byte", "type": "string"}, "blobstore2Info": {"$ref": "Blobstore2Info", "description": "Blobstore v2 info, set if reference_type is BLOBSTORE_REF and it refers to a v2 blob."}, "compositeMedia": {"description": "A composite media composed of one or more media objects, set if reference_type is COMPOSITE_MEDIA. The media length field must be set to the sum of the lengths of all composite media objects. Note: All composite media must have length specified.", "items": {"$ref": "CompositeMedia"}, "type": "array"}, "contentType": {"description": "MIME type of the data", "type": "string"}, "contentTypeInfo": {"$ref": "ContentTypeInfo", "description": "Extended content type information provided for <PERSON><PERSON> uploads."}, "cosmoBinaryReference": {"description": "A binary data reference for a media download. Serves as a technology-agnostic binary reference in some Google infrastructure. This value is a serialized storage_cosmo.BinaryReference proto. Storing it as bytes is a hack to get around the fact that the cosmo proto (as well as others it includes) doesn't support JavaScript. This prevents us from including the actual type of this field.", "format": "byte", "type": "string"}, "crc32cHash": {"description": "For Scotty Uploads: Scotty-provided hashes for uploads For Scotty Downloads: (WARNING: DO NOT USE WITHOUT PERMISSION FROM THE SCOTTY TEAM.) A Hash provided by the agent to be used to verify the data being downloaded. Currently only supported for inline payloads. Further, only crc32c_hash is currently supported.", "format": "uint32", "type": "integer"}, "diffChecksumsResponse": {"$ref": "DiffChecksumsResponse", "description": "Set if reference_type is DIFF_CHECKSUMS_RESPONSE."}, "diffDownloadResponse": {"$ref": "DiffDownloadResponse", "description": "Set if reference_type is DIFF_DOWNLOAD_RESPONSE."}, "diffUploadRequest": {"$ref": "DiffUploadRequest", "description": "Set if reference_type is DIFF_UPLOAD_REQUEST."}, "diffUploadResponse": {"$ref": "DiffUploadResponse", "description": "Set if reference_type is DIFF_UPLOAD_RESPONSE."}, "diffVersionResponse": {"$ref": "DiffVersionResponse", "description": "Set if reference_type is DIFF_VERSION_RESPONSE."}, "downloadParameters": {"$ref": "DownloadParameters", "description": "Parameters for a media download."}, "filename": {"description": "Original file name", "type": "string"}, "hash": {"deprecated": true, "description": "Deprecated, use one of explicit hash type fields instead. These two hash related fields will only be populated on Scotty based media uploads and will contain the content of the hash group in the NotificationRequest: http://cs/#google3/blobstore2/api/scotty/service/proto/upload_listener.proto&q=class:Hash Hex encoded hash value of the uploaded media.", "type": "string"}, "hashVerified": {"description": "For <PERSON><PERSON> uploads only. If a user sends a hash code and the backend has requested that <PERSON><PERSON> verify the upload against the client hash, <PERSON><PERSON> will perform the check on behalf of the backend and will reject it if the hashes don't match. This is set to true if <PERSON><PERSON> performed this verification.", "type": "boolean"}, "inline": {"description": "Media data, set if reference_type is INLINE", "format": "byte", "type": "string"}, "isPotentialRetry": {"description": "|is_potential_retry| is set false only when <PERSON><PERSON> is certain that it has not sent the request before. When a client resumes an upload, this field must be set true in agent calls, because <PERSON><PERSON> cannot be certain that it has never sent the request before due to potential failure in the session state persistence.", "type": "boolean"}, "length": {"description": "Size of the data, in bytes", "format": "int64", "type": "string"}, "md5Hash": {"description": "Scotty-provided MD5 hash for an upload.", "format": "byte", "type": "string"}, "mediaId": {"description": "Media id to forward to the operation GetMedia. Can be set if reference_type is GET_MEDIA.", "format": "byte", "type": "string"}, "objectId": {"$ref": "ObjectId", "description": "Reference to a TI Blob, set if reference_type is BIGSTORE_REF."}, "path": {"description": "Path to the data, set if reference_type is PATH", "type": "string"}, "referenceType": {"description": "Describes what the field reference contains.", "enum": ["PATH", "BLOB_REF", "INLINE", "GET_MEDIA", "COMPOSITE_MEDIA", "BIGSTORE_REF", "DIFF_VERSION_RESPONSE", "DIFF_CHECKSUMS_RESPONSE", "DIFF_DOWNLOAD_RESPONSE", "DIFF_UPLOAD_REQUEST", "DIFF_UPLOAD_RESPONSE", "COSMO_BINARY_REFERENCE", "ARBITRARY_BYTES"], "enumDescriptions": ["Reference contains a GFS path or a local path.", "Reference points to a blobstore object. This could be either a v1 blob_ref or a v2 blobstore2_info. Clients should check blobstore2_info first, since v1 is being deprecated.", "Data is included into this proto buffer", "Data should be accessed from the current service using the operation GetMedia.", "The content for this media object is stored across multiple partial media objects under the composite_media field.", "Reference points to a bigstore object", "Indicates the data is stored in diff_version_response.", "Indicates the data is stored in diff_checksums_response.", "Indicates the data is stored in diff_download_response.", "Indicates the data is stored in diff_upload_request.", "Indicates the data is stored in diff_upload_response.", "Indicates the data is stored in cosmo_binary_reference.", "Informs <PERSON><PERSON> to generate a response payload with the size specified in the length field. The contents of the payload are generated by <PERSON><PERSON> and are undefined. This is useful for testing download speeds between the user and <PERSON><PERSON> without involving a real payload source. Note: range is not supported when using arbitrary_bytes."], "type": "string"}, "sha1Hash": {"description": "Scotty-provided SHA1 hash for an upload.", "format": "byte", "type": "string"}, "sha256Hash": {"description": "Scotty-provided SHA256 hash for an upload.", "format": "byte", "type": "string"}, "timestamp": {"description": "Time at which the media data was last updated, in milliseconds since UNIX epoch", "format": "uint64", "type": "string"}, "token": {"description": "A unique fingerprint/version id for the media data", "type": "string"}}, "type": "object"}, "MediaRequestInfo": {"description": "Extra information added to operations that support Scotty media requests.", "id": "MediaRequestInfo", "properties": {"currentBytes": {"description": "The number of current bytes uploaded or downloaded.", "format": "int64", "type": "string"}, "customData": {"description": "Data to be copied to backend requests. Custom data is returned to <PERSON><PERSON> in the agent_state field, which <PERSON><PERSON> will then provide in subsequent upload notifications.", "type": "string"}, "diffObjectVersion": {"description": "Set if the http request info is diff encoded. The value of this field is the version number of the base revision. This is corresponding to Apiary's mediaDiffObjectVersion (//depot/google3/java/com/google/api/server/media/variable/DiffObjectVersionVariable.java). See go/esf-scotty-diff-upload for more information.", "type": "string"}, "finalStatus": {"description": "The existence of the final_status field indicates that this is the last call to the agent for this request_id. http://google3/uploader/agent/scotty_agent.proto?l=737&rcl=347601929", "format": "int32", "type": "integer"}, "notificationType": {"description": "The type of notification received from <PERSON><PERSON>.", "enum": ["START", "PROGRESS", "END", "RESPONSE_SENT", "ERROR"], "enumDescriptions": ["Such requests signals the start of a request containing media upload. Only the media field(s) in the inserted/updated resource are set. The response should either return an error or succeed. On success, responses don't need to contain anything.", "Such requests signals that the upload has progressed and that the backend might want to access the media file specified in relevant fields in the resource. Only the media field(s) in the inserted/updated resource are set. The response should either return an error or succeed. On success, responses don't need to contain anything.", "Such requests signals the end of a request containing media upload. END should be handled just like normal Insert/Upload requests, that is, they should process the request and return a complete resource in the response. Pointers to media data (a GFS path usually) appear in the relevant fields in the inserted/updated resource. See gdata.Media in data.proto.", "Such requests occur after an END and signal that the response has been sent back to the client. RESPONSE_SENT is only sent to the backend if it is configured to receive them. The response does not need to contain anything.", "Such requests indicate that an error occurred while processing the request. ERROR is only sent to the backend if it is configured to receive them. It is not guaranteed that all errors will result in this notification to the backend, even if the backend requests them. Since these requests are just for informational purposes, the response does not need to contain anything."], "type": "string"}, "requestId": {"description": "The Scotty request ID.", "type": "string"}, "requestReceivedParamsServingInfo": {"description": "The partition of the Scotty server handling this request. type is uploader_service.RequestReceivedParamsServingInfo LINT.IfChange(request_received_params_serving_info_annotations) LINT.ThenChange()", "format": "byte", "type": "string"}, "totalBytes": {"description": "The total size of the file.", "format": "int64", "type": "string"}, "totalBytesIsEstimated": {"description": "Whether the total bytes field contains an estimated data.", "type": "boolean"}}, "type": "object"}, "MerchantLocation": {"description": "Locations of interest for this class or object. Currently, this location is used for geofenced notifications. When a user is within a set radius of this lat/long, and dwells there, Google will trigger a notification. When a user exits this radius, the notification will be hidden.", "id": "MerchantLocation", "properties": {"latitude": {"description": "The latitude specified as any value in the range of -90.0 through +90.0, both inclusive. Values outside these bounds will be rejected.", "format": "double", "type": "number"}, "longitude": {"description": "The longitude specified in the range -180.0 through +180.0, both inclusive. Values outside these bounds will be rejected.", "format": "double", "type": "number"}}, "type": "object"}, "Message": {"description": "A message that will be displayed with a Valuable", "id": "Message", "properties": {"body": {"description": "The message body.", "type": "string"}, "displayInterval": {"$ref": "TimeInterval", "description": "The period of time that the message will be displayed to users. You can define both a `startTime` and `endTime` for each message. A message is displayed immediately after a Wallet Object is inserted unless a `startTime` is set. The message will appear in a list of messages indefinitely if `endTime` is not provided."}, "header": {"description": "The message header.", "type": "string"}, "id": {"description": "The ID associated with a message. This field is here to enable ease of management of messages. Notice ID values could possibly duplicate across multiple messages in the same class/instance, and care must be taken to select a reasonable ID for each message.", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#walletObjectMessage\"`.", "type": "string"}, "localizedBody": {"$ref": "LocalizedString", "description": "Translated strings for the message body."}, "localizedHeader": {"$ref": "LocalizedString", "description": "Translated strings for the message header."}, "messageType": {"description": "The message type.", "enum": ["MESSAGE_TYPE_UNSPECIFIED", "TEXT", "text", "EXPIRATION_NOTIFICATION", "expirationNotification", "TEXT_AND_NOTIFY"], "enumDeprecated": [false, false, true, false, true, false], "enumDescriptions": ["", "Renders the message as text on the card details screen. This is the default message type.", "Legacy alias for `TEXT`. Deprecated.", "Note: This enum is currently not supported.", "Legacy alias for `EXPIRATION_NOTIFICATION`. Deprecated.", "Renders the message as text on the card details screen and as an Android notification."], "type": "string"}}, "type": "object"}, "ModifyLinkedOfferObjects": {"id": "ModifyLinkedOfferObjects", "properties": {"addLinkedOfferObjectIds": {"description": "The linked offer object ids to add to the object.", "items": {"type": "string"}, "type": "array"}, "removeLinkedOfferObjectIds": {"description": "The linked offer object ids to remove from the object.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ModifyLinkedOfferObjectsRequest": {"id": "ModifyLinkedOfferObjectsRequest", "properties": {"linkedOfferObjectIds": {"$ref": "ModifyLinkedOfferObjects", "description": "The linked offer object ids to add or remove from the object."}}, "type": "object"}, "ModuleViewConstraints": {"description": "Constraints that all must be met for the module to be shown.", "id": "ModuleViewConstraints", "properties": {"displayInterval": {"$ref": "TimeInterval", "description": "The period of time that the module will be displayed to users. Can define both a `startTime` and `endTime`. The module is displayed immediately after insertion unless a `startTime` is set. The module is displayed indefinitely if `endTime` is not set."}}, "type": "object"}, "Money": {"id": "Money", "properties": {"currencyCode": {"description": "The currency code, such as \"USD\" or \"EUR.\"", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#money\"`.", "type": "string"}, "micros": {"description": "The unit of money amount in micros. For example, $1 USD would be represented as 1000000 micros.", "format": "int64", "type": "string"}}, "type": "object"}, "Notifications": {"description": "Indicates if the object needs to have notification enabled. We support only one of ExpiryNotification/UpcomingNotification. `expiryNotification` takes precedence over `upcomingNotification`. In other words if `expiryNotification` is set, we ignore the `upcomingNotification` field.", "id": "Notifications", "properties": {"expiryNotification": {"$ref": "ExpiryNotification", "description": "A notification would be triggered at a specific time before the card expires."}, "upcomingNotification": {"$ref": "UpcomingNotification", "description": "A notification would be triggered at a specific time before the card becomes usable."}}, "type": "object"}, "ObjectId": {"description": "This is a copy of the tech.blob.ObjectId proto, which could not be used directly here due to transitive closure issues with JavaScript support; see http://b/8801763.", "id": "ObjectId", "properties": {"bucketName": {"description": "The name of the bucket to which this object belongs.", "type": "string"}, "generation": {"description": "Generation of the object. Generations are monotonically increasing across writes, allowing them to be be compared to determine which generation is newer. If this is omitted in a request, then you are requesting the live object. See http://go/bigstore-versions", "format": "int64", "type": "string"}, "objectName": {"description": "The name of the object.", "type": "string"}}, "type": "object"}, "OfferClass": {"id": "OfferClass", "properties": {"allowMultipleUsersPerObject": {"deprecated": true, "description": "Deprecated. Use `multipleDevicesAndHoldersAllowedStatus` instead.", "type": "boolean"}, "appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding object that will be used instead."}, "callbackOptions": {"$ref": "CallbackOptions", "description": "Callback options to be used to call the issuer back for every save/delete of an object for this class by the end-user. All objects of this class are eligible for the callback."}, "classTemplateInfo": {"$ref": "ClassTemplateInfo", "description": "Template information about how the class should be displayed. If unset, Google will fallback to a default set of fields to display."}, "countryCode": {"description": "Country code used to display the card's country (when the user is not in that country), as well as to display localized content when content is not available in the user's locale.", "type": "string"}, "details": {"description": "The details of the offer.", "type": "string"}, "enableSmartTap": {"description": "Identifies whether this class supports Smart Tap. The `redemptionIssuers` and object level `smartTapRedemptionLevel` fields must also be set up correctly in order for a pass to support Smart Tap.", "type": "boolean"}, "finePrint": {"description": "The fine print or terms of the offer, such as \"20% off any t-shirt at Adam's Apparel.\"", "type": "string"}, "helpUri": {"$ref": "<PERSON><PERSON>", "description": "The help link for the offer, such as `http://myownpersonaldomain.com/help`"}, "heroImage": {"$ref": "Image", "description": "Optional banner image displayed on the front of the card. If none is present, nothing will be displayed. The image will display at 100% width."}, "hexBackgroundColor": {"description": "The background color for the card. If not set the dominant color of the hero image is used, and if no hero image is set, the dominant color of the logo is used. The format is #rrggbb where rrggbb is a hex RGB triplet, such as `#ffcc00`. You can also use the shorthand version of the RGB triplet which is #rgb, such as `#fc0`.", "type": "string"}, "homepageUri": {"$ref": "<PERSON><PERSON>", "description": "The URI of your application's home page. Populating the URI in this field results in the exact same behavior as populating an URI in linksModuleData (when an object is rendered, a link to the homepage is shown in what would usually be thought of as the linksModuleData section of the object)."}, "id": {"description": "Required. The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "type": "string"}, "imageModulesData": {"description": "Image module data. The maximum number of these fields displayed is 1 from object level and 1 for class object level.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "infoModuleData": {"$ref": "InfoModuleData", "deprecated": true, "description": "Deprecated. Use textModulesData instead."}, "issuerName": {"description": "Required. The issuer name. Recommended maximum length is 20 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#offerClass\"`.", "type": "string"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If links module data is also defined on the object, both will be displayed."}, "localizedDetails": {"$ref": "LocalizedString", "description": "Translated strings for the details."}, "localizedFinePrint": {"$ref": "LocalizedString", "description": "Translated strings for the fine_print."}, "localizedIssuerName": {"$ref": "LocalizedString", "description": "Translated strings for the issuer_name. Recommended maximum length is 20 characters to ensure full string is displayed on smaller screens."}, "localizedProvider": {"$ref": "LocalizedString", "description": "Translated strings for the provider. Recommended maximum length is 12 characters to ensure full string is displayed on smaller screens."}, "localizedShortTitle": {"$ref": "LocalizedString", "description": "Translated strings for the short title. Recommended maximum length is 20 characters."}, "localizedTitle": {"$ref": "LocalizedString", "description": "Translated strings for the title. Recommended maximum length is 60 characters to ensure full string is displayed on smaller screens."}, "locations": {"deprecated": true, "description": "Note: This field is currently not supported to trigger geo notifications.", "items": {"$ref": "LatLongPoint"}, "type": "array"}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the class. Any additional MerchantLocations added beyond the 10 will be rejected. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "multipleDevicesAndHoldersAllowedStatus": {"description": "Identifies whether multiple users and devices will save the same object referencing this class.", "enum": ["STATUS_UNSPECIFIED", "MULTIPLE_HOLDERS", "ONE_USER_ALL_DEVICES", "ONE_USER_ONE_DEVICE", "multipleHolders", "oneUserAllDevices", "oneUserOneDevice"], "enumDeprecated": [false, false, false, false, true, true, true], "enumDescriptions": ["Unspecified preference.", "The Pass object is shareable by a user and can be saved by any number of different users, and on any number of devices. Partners typically use this setup for passes that do not need to be restricted to a single user or pinned to a single device.", "An object can only be saved by one user, but this user can view and use it on multiple of their devices. Once the first user saves the object, no other user will be allowed to view or save it.", "An object can only be saved by one user on a single device. Intended for use by select partners in limited circumstances. An example use case is a transit ticket that should be \"device pinned\", meaning it can be saved, viewed and used only by a single user on a single device. Contact support for additional information.", "Legacy alias for `MULTIP<PERSON>_HOLDERS`. Deprecated.", "Legacy alias for `ONE_USER_ALL_DEVICES`. Deprecated.", "Legacy alias for `ONE_USER_ONE_DEVICE`. Deprecated."], "type": "string"}, "notifyPreference": {"description": "Whether or not field updates to this class should trigger notifications. When set to NOTIFY, we will attempt to trigger a field update notification to users. These notifications will only be sent to users if the field is part of an allowlist. If not specified, no notification will be triggered. This setting is ephemeral and needs to be set with each PATCH or UPDATE request, otherwise a notification will not be triggered.", "enum": ["NOTIFICATION_SETTINGS_FOR_UPDATES_UNSPECIFIED", "NOTIFY_ON_UPDATE"], "enumDescriptions": ["Default behavior is no notifications sent.", "This value will result in a notification being sent, if the updated fields are part of an allowlist."], "type": "string"}, "provider": {"description": "Required. The offer provider (either the aggregator name or merchant name). Recommended maximum length is 12 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "redemptionChannel": {"description": "Required. The redemption channels applicable to this offer.", "enum": ["REDEMPTION_CHANNEL_UNSPECIFIED", "INSTORE", "instore", "ONLINE", "online", "BOTH", "both", "TEMPORARY_PRICE_REDUCTION", "temporaryPriceReduction"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `INSTORE`. Deprecated.", "", "Legacy alias for `ONLINE`. Deprecated.", "", "Legacy alias for `BOTH`. Deprecated.", "", "Legacy alias for `TEMPORARY_PRICE_REDUCTION`. Deprecated."], "type": "string"}, "redemptionIssuers": {"description": "Identifies which redemption issuers can redeem the pass over Smart Tap. Redemption issuers are identified by their issuer ID. Redemption issuers must have at least one Smart Tap key configured. The `enableSmartTap` and object level `smartTapRedemptionLevel` fields must also be set up correctly in order for a pass to support Smart Tap.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "review": {"$ref": "Review", "description": "The review comments set by the platform when a class is marked `approved` or `rejected`."}, "reviewStatus": {"description": "Required. The status of the class. This field can be set to `draft` or The status of the class. This field can be set to `draft` or `underReview` using the insert, patch, or update API calls. Once the review state is changed from `draft` it may not be changed back to `draft`. You should keep this field to `draft` when the class is under development. A `draft` class cannot be used to create any object. You should set this field to `underReview` when you believe the class is ready for use. The platform will automatically set this field to `approved` and it can be immediately used to create or migrate objects. When updating an already `approved` class you should keep setting this field to `underReview`.", "enum": ["REVIEW_STATUS_UNSPECIFIED", "UNDER_REVIEW", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "APPROVED", "approved", "REJECTED", "rejected", "DRAFT", "draft"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `UNDER_REVIEW`. Deprecated.", "", "Legacy alias for `APPROVED`. Deprecated.", "", "Legacy alias for `REJECTED`. Deprecated.", "", "Legacy alias for `DRAFT`. Deprecated."], "type": "string"}, "securityAnimation": {"$ref": "SecurityAnimation", "description": "Optional information about the security animation. If this is set a security animation will be rendered on pass details."}, "shortTitle": {"description": "A shortened version of the title of the offer, such as \"20% off,\" shown to users as a quick reference to the offer contents. Recommended maximum length is 20 characters.", "type": "string"}, "textModulesData": {"description": "Text module data. If text module data is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from the object and 10 from the class.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "title": {"description": "Required. The title of the offer, such as \"20% off any t-shirt.\" Recommended maximum length is 60 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "titleImage": {"$ref": "Image", "description": "The title image of the offer. This image is displayed in both the details and list views of the app."}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the class. For a pass only ten will be displayed, prioritizing those from the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "version": {"deprecated": true, "description": "Deprecated", "format": "int64", "type": "string"}, "viewUnlockRequirement": {"description": "View Unlock Requirement options for the offer.", "enum": ["VIEW_UNLOCK_REQUIREMENT_UNSPECIFIED", "UNLOCK_NOT_REQUIRED", "UNLOCK_REQUIRED_TO_VIEW"], "enumDescriptions": ["Default value, same as UNLOCK_NOT_REQUIRED.", "Default behavior for all the existing Passes if ViewUnlockRequirement is not set.", "Requires the user to unlock their device each time the pass is viewed. If the user removes their device lock after saving the pass, then they will be prompted to create a device lock before the pass can be viewed."], "type": "string"}, "wideTitleImage": {"$ref": "Image", "description": "The wide title image of the offer. When provided, this will be used in place of the title image in the top left of the card view."}, "wordMark": {"$ref": "Image", "deprecated": true, "description": "Deprecated."}}, "type": "object"}, "OfferClassAddMessageResponse": {"id": "OfferClassAddMessageResponse", "properties": {"resource": {"$ref": "OfferClass", "description": "The updated OfferClass resource."}}, "type": "object"}, "OfferClassListResponse": {"id": "OfferClassListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "OfferClass"}, "type": "array"}}, "type": "object"}, "OfferObject": {"id": "OfferObject", "properties": {"appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding class only object AppLinkData will be displayed."}, "barcode": {"$ref": "Barcode", "description": "The barcode type and value."}, "classId": {"description": "Required. The class associated with this object. The class must be of the same type as this object, must already exist, and must be approved. Class IDs should follow the format issuer ID.identifier where the former is issued by Google and latter is chosen by you.", "type": "string"}, "classReference": {"$ref": "OfferClass", "description": "A copy of the inherited fields of the parent class. These fields are retrieved during a GET."}, "disableExpirationNotification": {"description": "Indicates if notifications should explicitly be suppressed. If this field is set to true, regardless of the `messages` field, expiration notifications to the user will be suppressed. By default, this field is set to false. Currently, this can only be set for offers.", "type": "boolean"}, "groupingInfo": {"$ref": "GroupingInfo", "description": "Information that controls how passes are grouped together."}, "hasLinkedDevice": {"description": "Whether this object is currently linked to a single device. This field is set by the platform when a user saves the object, linking it to their device. Intended for use by select partners. Contact support for additional information.", "type": "boolean"}, "hasUsers": {"description": "Indicates if the object has users. This field is set by the platform.", "type": "boolean"}, "heroImage": {"$ref": "Image", "description": "Optional banner image displayed on the front of the card. If none is present, hero image of the class, if present, will be displayed. If hero image of the class is also not present, nothing will be displayed."}, "id": {"description": "Required. The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID.identifier where the former is issued by Google and latter is chosen by you. The unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "type": "string"}, "imageModulesData": {"description": "Image module data. The maximum number of these fields displayed is 1 from object level and 1 for class object level.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "infoModuleData": {"$ref": "InfoModuleData", "description": "Deprecated. Use textModulesData instead."}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#offerObject\"`.", "type": "string"}, "linkedObjectIds": {"description": "linked_object_ids are a list of other objects such as event ticket, loyalty, offer, generic, giftcard, transit and boarding pass that should be automatically attached to this offer object. If a user had saved this offer, then these linked_object_ids would be automatically pushed to the user's wallet (unless they turned off the setting to receive such linked passes). Make sure that objects present in linked_object_ids are already inserted - if not, calls would fail. Once linked, the linked objects cannot be unlinked. You cannot link objects belonging to another issuer. There is a limit to the number of objects that can be linked to a single object. After the limit is reached, new linked objects in the call will be ignored silently. Object IDs should follow the format issuer ID.identifier where the former is issued by Google and the latter is chosen by you.", "items": {"type": "string"}, "type": "array"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If links module data is also defined on the class, both will be displayed."}, "locations": {"description": "Note: This field is currently not supported to trigger geo notifications.", "items": {"$ref": "LatLongPoint"}, "type": "array"}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the object. Any additional MerchantLocations added beyond the 10 will be rejected. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "notifyPreference": {"description": "Whether or not field updates to this object should trigger notifications. When set to NOTIFY, we will attempt to trigger a field update notification to users. These notifications will only be sent to users if the field is part of an allowlist. If set to DO_NOT_NOTIFY or NOTIFICATION_SETTINGS_UNSPECIFIED, no notification will be triggered. This setting is ephemeral and needs to be set with each PATCH or UPDATE request, otherwise a notification will not be triggered.", "enum": ["NOTIFICATION_SETTINGS_FOR_UPDATES_UNSPECIFIED", "NOTIFY_ON_UPDATE"], "enumDescriptions": ["Default behavior is no notifications sent.", "This value will result in a notification being sent, if the updated fields are part of an allowlist."], "type": "string"}, "passConstraints": {"$ref": "PassConstraints", "description": "Pass constraints for the object. Includes limiting NFC and screenshot behaviors."}, "rotatingBarcode": {"$ref": "RotatingBarcode", "description": "The rotating barcode type and value."}, "saveRestrictions": {"$ref": "SaveRestrictions", "description": "Restrictions on the object that needs to be verified before the user tries to save the pass. Note that this restrictions will only be applied during save time. If the restrictions changed after a user saves the pass, the new restrictions will not be applied to an already saved pass."}, "smartTapRedemptionValue": {"description": "The value that will be transmitted to a Smart Tap certified terminal over NFC for this object. The class level fields `enableSmartTap` and `redemptionIssuers` must also be set up correctly in order for the pass to support Smart Tap. Only ASCII characters are supported.", "type": "string"}, "state": {"description": "Required. The state of the object. This field is used to determine how an object is displayed in the app. For example, an `inactive` object is moved to the \"Expired passes\" section.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "active", "COMPLETED", "completed", "EXPIRED", "expired", "INACTIVE", "inactive"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["Default value.", "Object is active and displayed to with other active objects.", "Legacy alias for `ACTIVE`. Deprecated.", "Object has completed it's lifecycle.", "Legacy alias for `COMPLETED`. Deprecated.", "Object is no longer valid (`validTimeInterval` passed).", "Legacy alias for `EXPIRED`. Deprecated.", "Object is no longer valid", "Legacy alias for `INACTIVE`. Deprecated."], "type": "string"}, "textModulesData": {"description": "Text module data. If text module data is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from the object and 10 from the class.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "validTimeInterval": {"$ref": "TimeInterval", "description": "The time period this object will be `active` and object can be used. An object's state will be changed to `expired` when this time period has passed."}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "version": {"deprecated": true, "description": "Deprecated", "format": "int64", "type": "string"}}, "type": "object"}, "OfferObjectAddMessageResponse": {"id": "OfferObjectAddMessageResponse", "properties": {"resource": {"$ref": "OfferObject", "description": "The updated OfferObject resource."}}, "type": "object"}, "OfferObjectListResponse": {"id": "OfferObjectListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "OfferObject"}, "type": "array"}}, "type": "object"}, "Pagination": {"id": "Pagination", "properties": {"kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#pagination\"`.", "type": "string"}, "nextPageToken": {"description": "Page token to send to fetch the next page.", "type": "string"}, "resultsPerPage": {"description": "Number of results returned in this page.", "format": "int32", "type": "integer"}}, "type": "object"}, "PassConstraints": {"description": "Container for any constraints that may be placed on passes.", "id": "PassConstraints", "properties": {"nfcConstraint": {"description": "The NFC constraints for the pass.", "items": {"enum": ["NFC_CONSTRAINT_UNSPECIFIED", "BLOCK_PAYMENT", "BLOCK_CLOSED_LOOP_TRANSIT"], "enumDescriptions": ["Default value, no specified constraint.", "Payment cards will not be conveyed while the pass is open.", "Closed loop transit cards will not be conveyed while the pass is open."], "type": "string"}, "type": "array"}, "screenshotEligibility": {"description": "The screenshot eligibility for the pass.", "enum": ["SCREENSHOT_ELIGIBILITY_UNSPECIFIED", "ELIGIBLE", "INELIGIBLE"], "enumDescriptions": ["Default value, same as ELIGIBLE.", "Default behavior for all existing Passes if ScreenshotEligibility is not set. Allows screenshots to be taken on Android devices.", "Disallows screenshots to be taken on Android devices. Note that older versions of Wallet may still allow screenshots to be taken."], "type": "string"}}, "type": "object"}, "Permission": {"id": "Permission", "properties": {"emailAddress": {"description": "The email address of the user, group, or service account to which this permission refers to.", "type": "string"}, "role": {"description": "The role granted by this permission.", "enum": ["ROLE_UNSPECIFIED", "OWNER", "owner", "READER", "reader", "WRITER", "writer"], "enumDeprecated": [false, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `OWNER`. Deprecated.", "", "Legacy alias for `READER`. Deprecated.", "", "Legacy alias for `WRITER`. Deprecated."], "type": "string"}}, "type": "object"}, "Permissions": {"id": "Permissions", "properties": {"issuerId": {"description": "ID of the issuer the list of permissions refer to.", "format": "int64", "type": "string"}, "permissions": {"description": "The complete list of permissions for the issuer account.", "items": {"$ref": "Permission"}, "type": "array"}}, "type": "object"}, "PurchaseDetails": {"id": "PurchaseDetails", "properties": {"accountId": {"description": "ID of the account used to purchase the ticket.", "type": "string"}, "confirmationCode": {"description": "The confirmation code for the purchase. This may be the same for multiple different tickets and is used to group tickets together.", "type": "string"}, "purchaseDateTime": {"description": "The purchase date/time of the ticket. This is an ISO 8601 extended format date/time, with or without an offset. Time may be specified up to nanosecond precision. Offsets may be specified with seconds precision (even though offset seconds is not part of ISO 8601). For example: `1985-04-12T23:20:50.52Z` would be 20 minutes and 50.52 seconds after the 23rd hour of April 12th, 1985 in UTC. `1985-04-12T19:20:50.52-04:00` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985, 4 hours before UTC (same instant in time as the above example). If the event were in New York, this would be the equivalent of Eastern Daylight Time (EDT). Remember that offset varies in regions that observe Daylight Saving Time (or Summer Time), depending on the time of the year. `1985-04-12T19:20:50.52` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985 with no offset information. Without offset information, some rich features may not be available.", "type": "string"}, "purchaseReceiptNumber": {"description": "Receipt number/identifier for tracking the ticket purchase via the body that sold the ticket.", "type": "string"}, "ticketCost": {"$ref": "TicketCost", "description": "The cost of the ticket."}}, "type": "object"}, "ReservationInfo": {"id": "ReservationInfo", "properties": {"confirmationCode": {"description": "Confirmation code needed to check into this flight. This is the number that the passenger would enter into a kiosk at the airport to look up the flight and print a boarding pass.", "type": "string"}, "eticketNumber": {"description": "E-ticket number.", "type": "string"}, "frequentFlyerInfo": {"$ref": "FrequentFlyerInfo", "description": "Frequent flyer membership information."}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#reservationInfo\"`.", "type": "string"}}, "type": "object"}, "Resources": {"id": "Resources", "properties": {"eventTicketClasses": {"description": "A list of event ticket classes.", "items": {"$ref": "EventTicketClass"}, "type": "array"}, "eventTicketObjects": {"description": "A list of event ticket objects.", "items": {"$ref": "EventTicketObject"}, "type": "array"}, "flightClasses": {"description": "A list of flight classes.", "items": {"$ref": "FlightClass"}, "type": "array"}, "flightObjects": {"description": "A list of flight objects.", "items": {"$ref": "FlightObject"}, "type": "array"}, "genericClasses": {"description": "A list of generic classes.", "items": {"$ref": "GenericClass"}, "type": "array"}, "genericObjects": {"description": "A list of generic objects.", "items": {"$ref": "GenericObject"}, "type": "array"}, "giftCardClasses": {"description": "A list of gift card classes.", "items": {"$ref": "GiftCardClass"}, "type": "array"}, "giftCardObjects": {"description": "A list of gift card objects.", "items": {"$ref": "GiftCardObject"}, "type": "array"}, "loyaltyClasses": {"description": "A list of loyalty classes.", "items": {"$ref": "LoyaltyClass"}, "type": "array"}, "loyaltyObjects": {"description": "A list of loyalty objects.", "items": {"$ref": "LoyaltyObject"}, "type": "array"}, "offerClasses": {"description": "A list of offer classes.", "items": {"$ref": "OfferClass"}, "type": "array"}, "offerObjects": {"description": "A list of offer objects.", "items": {"$ref": "OfferObject"}, "type": "array"}, "transitClasses": {"description": "A list of transit classes.", "items": {"$ref": "TransitClass"}, "type": "array"}, "transitObjects": {"description": "A list of transit objects.", "items": {"$ref": "TransitObject"}, "type": "array"}}, "type": "object"}, "Review": {"id": "Review", "properties": {"comments": {"type": "string"}}, "type": "object"}, "RotatingBarcode": {"id": "RotatingBarcode", "properties": {"alternateText": {"description": "An optional text that will override the default text that shows under the barcode. This field is intended for a human readable equivalent of the barcode value, used when the barcode cannot be scanned.", "type": "string"}, "initialRotatingBarcodeValues": {"$ref": "RotatingBarcodeValues", "description": "Input only. NOTE: This feature is only available for the transit vertical. Optional set of initial rotating barcode values. This allows a small subset of barcodes to be included with the object. Further rotating barcode values must be uploaded with the UploadRotatingBarcodeValues endpoint."}, "renderEncoding": {"description": "The render encoding for the barcode. When specified, barcode is rendered in the given encoding. Otherwise best known encoding is chosen by Google.", "enum": ["RENDER_ENCODING_UNSPECIFIED", "UTF_8"], "enumDescriptions": ["", "UTF_8 encoding for barcodes. This is only supported for barcode type qrCode."], "type": "string"}, "showCodeText": {"$ref": "LocalizedString", "description": "Optional text that will be shown when the barcode is hidden behind a click action. This happens in cases where a pass has Smart Tap enabled. If not specified, a default is chosen by Google."}, "totpDetails": {"$ref": "RotatingBarcodeTotpDetails", "description": "Details used to evaluate the {totp_value_n} substitutions."}, "type": {"description": "The type of this barcode.", "enum": ["BARCODE_TYPE_UNSPECIFIED", "AZTEC", "aztec", "CODE_39", "code39", "CODE_128", "code128", "CODABAR", "codabar", "DATA_MATRIX", "dataMatrix", "EAN_8", "ean8", "EAN_13", "ean13", "EAN13", "ITF_14", "itf14", "PDF_417", "pdf417", "PDF417", "QR_CODE", "qrCode", "qrcode", "UPC_A", "upcA", "TEXT_ONLY", "textOnly"], "enumDeprecated": [false, false, true, false, true, false, true, false, true, false, true, false, true, false, true, true, false, true, false, true, true, false, true, true, false, true, false, true], "enumDescriptions": ["", "Not supported for Rotating Barcodes.", "Legacy alias for `AZTEC`. Deprecated. Not supported for Rotating Barcodes.", "Not supported for Rotating Barcodes.", "Legacy alias for `CODE_39`. Deprecated. Not supported for Rotating Barcodes.", "Not supported for Rotating Barcodes.", "Legacy alias for `CODE_128`. Deprecated. Not supported for Rotating Barcodes.", "Not supported for Rotating Barcodes.", "Legacy alias for `CODABAR`. Deprecated. Not supported for Rotating Barcodes.", "A 2D matrix barcode consisting of black and white. Cells or modules are arranged in either a square or rectangle. Not supported for Rotating Barcodes.", "Legacy alias for `DATA_MATRIX`. Deprecated. Not supported for Rotating Barcodes.", "Not supported for Rotating Barcodes.", "Legacy alias for `EAN_8`. Deprecated. Not supported for Rotating Barcodes.", "Not supported for Rotating Barcodes.", "Legacy alias for `EAN_13`. Deprecated. Not supported for Rotating Barcodes.", "Legacy alias for `EAN_13`. Deprecated. Not supported for Rotating Barcodes.", "14 digit ITF code Not supported for Rotating Barcodes.", "Legacy alias for `ITF_14`. Deprecated. Not supported for Rotating Barcodes.", "Supported for Rotating Barcodes.", "Legacy alias for `PDF_417`. Deprecated.", "Legacy alias for `PDF_417`. Deprecated.", "Supported for Rotating Barcodes.", "Legacy alias for `QR_CODE`. Deprecated.", "Legacy alias for `QR_CODE`. Deprecated.", "11 or 12 digit codes Not supported for Rotating Barcodes.", "Legacy alias for `UPC_A`. Deprecated. Not supported for Rotating Barcodes.", "Renders the field as a text field. The `alternateText` field may not be used with a barcode of type `textOnly`. Not supported for Rotating Barcodes.", "Legacy alias for `TEXT_ONLY`. Deprecated. Not supported for Rotating Barcodes."], "type": "string"}, "valuePattern": {"description": "String encoded barcode value. This string supports the following substitutions: * {totp_value_n}: Replaced with the TOTP value (see TotpDetails.parameters). * {totp_timestamp_millis}: Replaced with the timestamp (millis since epoch) at which the barcode was generated. * {totp_timestamp_seconds}: Replaced with the timestamp (seconds since epoch) at which the barcode was generated.", "type": "string"}}, "type": "object"}, "RotatingBarcodeTotpDetails": {"description": "Configuration for the time-based OTP substitutions. See https://tools.ietf.org/html/rfc6238", "id": "RotatingBarcodeTotpDetails", "properties": {"algorithm": {"description": "The TOTP algorithm used to generate the OTP.", "enum": ["TOTP_ALGORITHM_UNSPECIFIED", "TOTP_SHA1"], "enumDescriptions": ["", "TOTP algorithm from RFC 6238 with the SHA1 hash function"], "type": "string"}, "parameters": {"description": "The TOTP parameters for each of the {totp_value_*} substitutions. The TotpParameters at index n is used for the {totp_value_n} substitution.", "items": {"$ref": "RotatingBarcodeTotpDetailsTotpParameters"}, "type": "array"}, "periodMillis": {"description": "The time interval used for the TOTP value generation, in milliseconds.", "format": "int64", "type": "string"}}, "type": "object"}, "RotatingBarcodeTotpDetailsTotpParameters": {"description": "Configuration for the key and value length. See https://www.rfc-editor.org/rfc/rfc4226#section-5.3", "id": "RotatingBarcodeTotpDetailsTotpParameters", "properties": {"key": {"description": "The secret key used for the TOTP value generation, encoded as a Base16 string.", "type": "string"}, "valueLength": {"description": "The length of the TOTP value in decimal digits.", "format": "int32", "type": "integer"}}, "type": "object"}, "RotatingBarcodeValues": {"description": "A payload containing many barcode values and start date/time.", "id": "RotatingBarcodeValues", "properties": {"periodMillis": {"description": "Required. The amount of time each barcode is valid for.", "format": "int64", "type": "string"}, "startDateTime": {"description": "Required. The date/time the first barcode is valid from. Barcodes will be rotated through using period_millis defined on the object's RotatingBarcodeValueInfo. This is an ISO 8601 extended format date/time, with an offset. Time may be specified up to nanosecond precision. Offsets may be specified with seconds precision (even though offset seconds is not part of ISO 8601). For example: `1985-04-12T23:20:50.52Z` would be 20 minutes and 50.52 seconds after the 23rd hour of April 12th, 1985 in UTC. `1985-04-12T19:20:50.52-04:00` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985, 4 hours before UTC (same instant in time as the above example). If the event were in New York, this would be the equivalent of Eastern Daylight Time (EDT). Remember that offset varies in regions that observe Daylight Saving Time (or Summer Time), depending on the time of the year.", "type": "string"}, "values": {"description": "Required. The values to encode in the barcode. At least one value is required.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "SaveRestrictions": {"description": "Defines restrictions on the object that will be verified during save. Note: this is an advanced feature, please contact Google for implementation support.", "id": "SaveRestrictions", "properties": {"restrictToEmailSha256": {"description": "Restrict the save of the referencing object to the given email address only. This is the hex output of SHA256 sum of the email address, all lowercase and without any notations like \".\" or \"+\", except \"@\". For example, for <EMAIL>, this value will be 31c5543c1734d25c7206f5fd591525d0295bec6fe84ff82f946a34fe970a1e66 <NAME_EMAIL>, this value will be bc34f262c93ad7122763684ccea6f07fb7f5d8a2d11e60ce15a6f43fe70ce632 If email address of the logged-in user who tries to save this pass does not match with the defined value here, users won't be allowed to save this pass. They will instead be prompted with an error to contact the issuer. This information should be gathered from the user with an explicit consent via Sign in with Google integration https://developers.google.com/identity/authentication. Please contact with support before using Save Restrictions.", "type": "string"}}, "type": "object"}, "SecurityAnimation": {"id": "SecurityAnimation", "properties": {"animationType": {"description": "Type of animation.", "enum": ["ANIMATION_UNSPECIFIED", "FOIL_SHIMMER", "foilShimmer"], "enumDeprecated": [false, false, true], "enumDescriptions": ["", "Default Foil & Shimmer animation", "Legacy alias for `FOIL_SHIMMER`. Deprecated."], "type": "string"}}, "type": "object"}, "SetPassUpdateNoticeRequest": {"description": "Request to send a private pass update notice information to Google, so that devices can then fetch the notice prompting the user to update a pass.", "id": "SetPassUpdateNoticeRequest", "properties": {"externalPassId": {"description": "Required. A fully qualified identifier of the pass that the issuer wants to notify the pass holder(s) about. Formatted as .", "type": "string"}, "updateUri": {"description": "Required. The issuer endpoint URI the pass holder needs to follow in order to receive an updated pass JWT. It can not contain any sensitive information. The endpoint needs to authenticate the user before giving the user the updated JWT. Example update URI https://someissuer.com/update/passId=someExternalPassId", "type": "string"}, "updatedPassJwtSignature": {"description": "Required. The JWT signature of the updated pass that the issuer wants to notify Google about. Only devices that report a different JWT signature than this JWT signature will receive the update notification.", "type": "string"}}, "type": "object"}, "SetPassUpdateNoticeResponse": {"description": "A response to a request to notify Google of an awaiting update to a private pass.", "id": "SetPassUpdateNoticeResponse", "properties": {}, "type": "object"}, "SignUpInfo": {"id": "SignUpInfo", "properties": {"classId": {"description": "ID of the class the user can sign up for.", "type": "string"}}, "type": "object"}, "SmartTap": {"id": "SmartTap", "properties": {"id": {"description": "The unique identifier for a smart tap. This value should follow the format issuer ID.identifier where the former is issued by Google and latter is the Smart Tap id. The Smart Tap id is a Base64 encoded string which represents the id which was generated by the Google Pay app.", "type": "string"}, "infos": {"description": "Communication from merchant to user.", "items": {"$ref": "IssuerToUserInfo"}, "type": "array"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#smartTap\"`.", "type": "string"}, "merchantId": {"description": "Smart Tap merchant ID of who engaged in the Smart Tap interaction.", "format": "int64", "type": "string"}}, "type": "object"}, "SmartTapMerchantData": {"id": "SmartTapMerchantData", "properties": {"authenticationKeys": {"description": "Available only to Smart Tap enabled partners. Contact support for additional guidance.", "items": {"$ref": "AuthenticationKey"}, "type": "array"}, "smartTapMerchantId": {"description": "Available only to Smart Tap enabled partners. Contact support for additional guidance.", "format": "int64", "type": "string"}}, "type": "object"}, "TemplateItem": {"id": "TemplateItem", "properties": {"firstValue": {"$ref": "FieldSelector", "description": "A reference to a field to display. If both `firstValue` and `secondValue` are populated, they will both appear as one item with a slash between them. For example, values A and B would be shown as \"A / B\"."}, "predefinedItem": {"description": "A predefined item to display. Only one of `firstValue` or `predefinedItem` may be set.", "enum": ["PREDEFINED_ITEM_UNSPECIFIED", "FREQUENT_FLYER_PROGRAM_NAME_AND_NUMBER", "frequentFlyerProgramNameAndNumber", "FLIGHT_NUMBER_AND_OPERATING_FLIGHT_NUMBER", "flightNumberAndOperatingFlightNumber"], "enumDeprecated": [false, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `FREQUENT_FLYER_PROGRAM_NAME_AND_NUMBER`. Deprecated.", "", "Legacy alias for `FLIGHT_NUMBER_AND_OPERATING_FLIGHT_NUMBER`. Deprecated."], "type": "string"}, "secondValue": {"$ref": "FieldSelector", "description": "A reference to a field to display. This may only be populated if the `firstValue` field is populated."}}, "type": "object"}, "TextModuleData": {"description": "Data for Text module. All fields are optional. Header will be displayed if available, different types of bodies will be concatenated if they are defined.", "id": "TextModuleData", "properties": {"body": {"description": "The body of the Text Module, which is defined as an uninterrupted string. Recommended maximum length is 500 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "header": {"description": "The header of the Text Module. Recommended maximum length is 35 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "id": {"description": "The ID associated with a text module. This field is here to enable ease of management of text modules and referencing them in template overrides. The ID should only include alphanumeric characters, '_', or '-'. It can not include dots, as dots are used to separate fields within FieldReference.fieldPaths in template overrides.", "type": "string"}, "localizedBody": {"$ref": "LocalizedString", "description": "Translated strings for the body. Recommended maximum length is 500 characters to ensure full string is displayed on smaller screens."}, "localizedHeader": {"$ref": "LocalizedString", "description": "Translated strings for the header. Recommended maximum length is 35 characters to ensure full string is displayed on smaller screens."}}, "type": "object"}, "TicketCost": {"id": "TicketCost", "properties": {"discountMessage": {"$ref": "LocalizedString", "description": "A message describing any kind of discount that was applied."}, "faceValue": {"$ref": "Money", "description": "The face value of the ticket."}, "purchasePrice": {"$ref": "Money", "description": "The actual purchase price of the ticket, after tax and/or discounts."}}, "type": "object"}, "TicketLeg": {"id": "TicketLeg", "properties": {"arrivalDateTime": {"description": "The date/time of arrival. This is an ISO 8601 extended format date/time, with or without an offset. Time may be specified up to nanosecond precision. Offsets may be specified with seconds precision (even though offset seconds is not part of ISO 8601). For example: `1985-04-12T23:20:50.52Z` would be 20 minutes and 50.52 seconds after the 23rd hour of April 12th, 1985 in UTC. `1985-04-12T19:20:50.52-04:00` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985, 4 hours before UTC (same instant in time as the above example). If the event were in New York, this would be the equivalent of Eastern Daylight Time (EDT). Remember that offset varies in regions that observe Daylight Saving Time (or Summer Time), depending on the time of the year. `1985-04-12T19:20:50.52` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985 with no offset information. The portion of the date/time without the offset is considered the \"local date/time\". This should be the local date/time at the destination station. For example, if the event occurs at the 20th hour of June 5th, 2018 at the destination station, the local date/time portion should be `2018-06-05T20:00:00`. If the local date/time at the destination station is 4 hours before UTC, an offset of `-04:00` may be appended. Without offset information, some rich features may not be available.", "type": "string"}, "carriage": {"description": "The train or ship name/number that the passsenger needs to board.", "type": "string"}, "departureDateTime": {"description": "The date/time of departure. This is required if there is no validity time interval set on the transit object. This is an ISO 8601 extended format date/time, with or without an offset. Time may be specified up to nanosecond precision. Offsets may be specified with seconds precision (even though offset seconds is not part of ISO 8601). For example: `1985-04-12T23:20:50.52Z` would be 20 minutes and 50.52 seconds after the 23rd hour of April 12th, 1985 in UTC. `1985-04-12T19:20:50.52-04:00` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985, 4 hours before UTC (same instant in time as the above example). If the event were in New York, this would be the equivalent of Eastern Daylight Time (EDT). Remember that offset varies in regions that observe Daylight Saving Time (or Summer Time), depending on the time of the year. `1985-04-12T19:20:50.52` would be 20 minutes and 50.52 seconds after the 19th hour of April 12th, 1985 with no offset information. The portion of the date/time without the offset is considered the \"local date/time\". This should be the local date/time at the origin station. For example, if the departure occurs at the 20th hour of June 5th, 2018 at the origin station, the local date/time portion should be `2018-06-05T20:00:00`. If the local date/time at the origin station is 4 hours before UTC, an offset of `-04:00` may be appended. Without offset information, some rich features may not be available.", "type": "string"}, "destinationName": {"$ref": "LocalizedString", "description": "The destination name."}, "destinationStationCode": {"description": "The destination station code.", "type": "string"}, "fareName": {"$ref": "LocalizedString", "description": "Short description/name of the fare for this leg of travel. Eg \"Anytime Single Use\"."}, "originName": {"$ref": "LocalizedString", "description": "The name of the origin station. This is required if `desinationName` is present or if `originStationCode` is not present."}, "originStationCode": {"description": "The origin station code. This is required if `destinationStationCode` is present or if `originName` is not present.", "type": "string"}, "platform": {"description": "The platform or gate where the passenger can board the carriage.", "type": "string"}, "ticketSeat": {"$ref": "TicketSeat", "description": "The reserved seat for the passenger(s). If more than one seat is to be specified then use the `ticketSeats` field instead. Both `ticketSeat` and `ticketSeats` may not be set."}, "ticketSeats": {"description": "The reserved seat for the passenger(s). If only one seat is to be specified then use the `ticketSeat` field instead. Both `ticketSeat` and `ticketSeats` may not be set.", "items": {"$ref": "TicketSeat"}, "type": "array"}, "transitOperatorName": {"$ref": "LocalizedString", "description": "The name of the transit operator that is operating this leg of a trip."}, "transitTerminusName": {"$ref": "LocalizedString", "description": "Terminus station or destination of the train/bus/etc."}, "zone": {"description": "The zone of boarding within the platform.", "type": "string"}}, "type": "object"}, "TicketRestrictions": {"id": "TicketRestrictions", "properties": {"otherRestrictions": {"$ref": "LocalizedString", "description": "Extra restrictions that don't fall under the \"route\" or \"time\" categories."}, "routeRestrictions": {"$ref": "LocalizedString", "description": "Restrictions about routes that may be taken. For example, this may be the string \"Reserved CrossCountry trains only\"."}, "routeRestrictionsDetails": {"$ref": "LocalizedString", "description": "More details about the above `routeRestrictions`."}, "timeRestrictions": {"$ref": "LocalizedString", "description": "Restrictions about times this ticket may be used."}}, "type": "object"}, "TicketSeat": {"id": "TicketSeat", "properties": {"coach": {"description": "The identifier of the train car or coach in which the ticketed seat is located. Eg. \"10\"", "type": "string"}, "customFareClass": {"$ref": "LocalizedString", "description": "A custome fare class to be used if no `fareClass` applies. Both `fareClass` and `customFareClass` may not be set."}, "fareClass": {"description": "The fare class of the ticketed seat.", "enum": ["FARE_CLASS_UNSPECIFIED", "ECONOMY", "economy", "FIRST", "first", "BUSINESS", "business"], "enumDeprecated": [false, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `ECONOMY`. Deprecated.", "", "Legacy alias for `FIRST`. Deprecated.", "", "Legacy alias for `BUSINESS`. Deprecated."], "type": "string"}, "seat": {"description": "The identifier of where the ticketed seat is located. Eg. \"42\". If there is no specific identifier, use `seatAssigment` instead.", "type": "string"}, "seatAssignment": {"$ref": "LocalizedString", "description": "The passenger's seat assignment. Eg. \"no specific seat\". To be used when there is no specific identifier to use in `seat`."}}, "type": "object"}, "TimeInterval": {"id": "TimeInterval", "properties": {"end": {"$ref": "DateTime", "description": "End time of the interval. Offset is not required. If an offset is provided and `start` time is set, `start` must also include an offset."}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#timeInterval\"`.", "type": "string"}, "start": {"$ref": "DateTime", "description": "Start time of the interval. Offset is not required. If an offset is provided and `end` time is set, `end` must also include an offset."}}, "type": "object"}, "TransitClass": {"id": "TransitClass", "properties": {"activationOptions": {"$ref": "ActivationOptions", "description": "Activation options for an activatable ticket."}, "allowMultipleUsersPerObject": {"deprecated": true, "description": "Deprecated. Use `multipleDevicesAndHoldersAllowedStatus` instead.", "type": "boolean"}, "appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding object that will be used instead."}, "callbackOptions": {"$ref": "CallbackOptions", "description": "Callback options to be used to call the issuer back for every save/delete of an object for this class by the end-user. All objects of this class are eligible for the callback."}, "classTemplateInfo": {"$ref": "ClassTemplateInfo", "description": "Template information about how the class should be displayed. If unset, Google will fallback to a default set of fields to display."}, "countryCode": {"description": "Country code used to display the card's country (when the user is not in that country), as well as to display localized content when content is not available in the user's locale.", "type": "string"}, "customCarriageLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the carriage value (`transitObject.ticketLeg.carriage`)."}, "customCoachLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the coach value (`transitObject.ticketLeg.ticketSeat.coach`)."}, "customConcessionCategoryLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the transit concession category value (`transitObject.concessionCategory`)."}, "customConfirmationCodeLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the confirmation code value (`transitObject.purchaseDetails.confirmationCode`)."}, "customDiscountMessageLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the transit discount message value (`transitObject.purchaseDetails.ticketCost.discountMessage`)."}, "customFareClassLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the fare class value (`transitObject.ticketLeg.ticketSeat.fareClass`)."}, "customFareNameLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the transit fare name value (`transitObject.ticketLeg.fareName`)."}, "customOtherRestrictionsLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the other restrictions value (`transitObject.ticketRestrictions.otherRestrictions`)."}, "customPlatformLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the boarding platform value (`transitObject.ticketLeg.platform`)."}, "customPurchaseFaceValueLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the purchase face value (`transitObject.purchaseDetails.ticketCost.faceValue`)."}, "customPurchasePriceLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the purchase price value (`transitObject.purchaseDetails.ticketCost.purchasePrice`)."}, "customPurchaseReceiptNumberLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the purchase receipt number value (`transitObject.purchaseDetails.purchaseReceiptNumber`)."}, "customRouteRestrictionsDetailsLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the route restrictions details value (`transitObject.ticketRestrictions.routeRestrictionsDetails`)."}, "customRouteRestrictionsLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the route restrictions value (`transitObject.ticketRestrictions.routeRestrictions`)."}, "customSeatLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the seat location value (`transitObject.ticketLeg.ticketSeat.seat`)."}, "customTicketNumberLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the ticket number value (`transitObject.ticketNumber`)."}, "customTimeRestrictionsLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the time restrictions details value (`transitObject.ticketRestrictions.timeRestrictions`)."}, "customTransitTerminusNameLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the transit terminus name value (`transitObject.ticketLeg.transitTerminusName`)."}, "customZoneLabel": {"$ref": "LocalizedString", "description": "A custom label to use for the boarding zone value (`transitObject.ticketLeg.zone`)."}, "enableSingleLegItinerary": {"description": "Controls the display of the single-leg itinerary for this class. By default, an itinerary will only display for multi-leg trips.", "type": "boolean"}, "enableSmartTap": {"description": "Identifies whether this class supports Smart Tap. The `redemptionIssuers` and object level `smartTapRedemptionLevel` fields must also be set up correctly in order for a pass to support Smart Tap.", "type": "boolean"}, "heroImage": {"$ref": "Image", "description": "Optional banner image displayed on the front of the card. If none is present, nothing will be displayed. The image will display at 100% width."}, "hexBackgroundColor": {"description": "The background color for the card. If not set the dominant color of the hero image is used, and if no hero image is set, the dominant color of the logo is used. The format is #rrggbb where rrggbb is a hex RGB triplet, such as `#ffcc00`. You can also use the shorthand version of the RGB triplet which is #rgb, such as `#fc0`.", "type": "string"}, "homepageUri": {"$ref": "<PERSON><PERSON>", "description": "The URI of your application's home page. Populating the URI in this field results in the exact same behavior as populating an URI in linksModuleData (when an object is rendered, a link to the homepage is shown in what would usually be thought of as the linksModuleData section of the object)."}, "id": {"description": "Required. The unique identifier for a class. This ID must be unique across all classes from an issuer. This value should follow the format issuer ID. identifier where the former is issued by Google and latter is chosen by you. Your unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "type": "string"}, "imageModulesData": {"description": "Image module data. The maximum number of these fields displayed is 1 from object level and 1 for class object level.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "infoModuleData": {"$ref": "InfoModuleData", "deprecated": true, "description": "Deprecated. Use textModulesData instead."}, "issuerName": {"description": "Required. The issuer name. Recommended maximum length is 20 characters to ensure full string is displayed on smaller screens.", "type": "string"}, "languageOverride": {"description": "If this field is present, transit tickets served to a user's device will always be in this language. Represents the BCP 47 language tag. Example values are \"en-US\", \"en-GB\", \"de\", or \"de-AT\".", "type": "string"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If links module data is also defined on the object, both will be displayed."}, "localizedIssuerName": {"$ref": "LocalizedString", "description": "Translated strings for the issuer_name. Recommended maximum length is 20 characters to ensure full string is displayed on smaller screens."}, "locations": {"deprecated": true, "description": "Note: This field is currently not supported to trigger geo notifications.", "items": {"$ref": "LatLongPoint"}, "type": "array"}, "logo": {"$ref": "Image", "description": "Required. The logo image of the ticket. This image is displayed in the card detail view of the app."}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the class. Any additional MerchantLocations added beyond the 10 will be rejected. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "multipleDevicesAndHoldersAllowedStatus": {"description": "Identifies whether multiple users and devices will save the same object referencing this class.", "enum": ["STATUS_UNSPECIFIED", "MULTIPLE_HOLDERS", "ONE_USER_ALL_DEVICES", "ONE_USER_ONE_DEVICE", "multipleHolders", "oneUserAllDevices", "oneUserOneDevice"], "enumDeprecated": [false, false, false, false, true, true, true], "enumDescriptions": ["Unspecified preference.", "The Pass object is shareable by a user and can be saved by any number of different users, and on any number of devices. Partners typically use this setup for passes that do not need to be restricted to a single user or pinned to a single device.", "An object can only be saved by one user, but this user can view and use it on multiple of their devices. Once the first user saves the object, no other user will be allowed to view or save it.", "An object can only be saved by one user on a single device. Intended for use by select partners in limited circumstances. An example use case is a transit ticket that should be \"device pinned\", meaning it can be saved, viewed and used only by a single user on a single device. Contact support for additional information.", "Legacy alias for `MULTIP<PERSON>_HOLDERS`. Deprecated.", "Legacy alias for `ONE_USER_ALL_DEVICES`. Deprecated.", "Legacy alias for `ONE_USER_ONE_DEVICE`. Deprecated."], "type": "string"}, "notifyPreference": {"description": "Whether or not field updates to this class should trigger notifications. When set to NOTIFY, we will attempt to trigger a field update notification to users. These notifications will only be sent to users if the field is part of an allowlist. If set to DO_NOT_NOTIFY or NOTIFICATION_SETTINGS_UNSPECIFIED, no notification will be triggered. This setting is ephemeral and needs to be set with each PATCH or UPDATE request, otherwise a notification will not be triggered.", "enum": ["NOTIFICATION_SETTINGS_FOR_UPDATES_UNSPECIFIED", "NOTIFY_ON_UPDATE"], "enumDescriptions": ["Default behavior is no notifications sent.", "This value will result in a notification being sent, if the updated fields are part of an allowlist."], "type": "string"}, "redemptionIssuers": {"description": "Identifies which redemption issuers can redeem the pass over Smart Tap. Redemption issuers are identified by their issuer ID. Redemption issuers must have at least one Smart Tap key configured. The `enableSmartTap` and object level `smartTapRedemptionLevel` fields must also be set up correctly in order for a pass to support Smart Tap.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "review": {"$ref": "Review", "description": "The review comments set by the platform when a class is marked `approved` or `rejected`."}, "reviewStatus": {"description": "Required. The status of the class. This field can be set to `draft` or `underReview` using the insert, patch, or update API calls. Once the review state is changed from `draft` it may not be changed back to `draft`. You should keep this field to `draft` when the class is under development. A `draft` class cannot be used to create any object. You should set this field to `underReview` when you believe the class is ready for use. The platform will automatically set this field to `approved` and it can be immediately used to create or migrate objects. When updating an already `approved` class you should keep setting this field to `underReview`.", "enum": ["REVIEW_STATUS_UNSPECIFIED", "UNDER_REVIEW", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "APPROVED", "approved", "REJECTED", "rejected", "DRAFT", "draft"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `UNDER_REVIEW`. Deprecated.", "", "Legacy alias for `APPROVED`. Deprecated.", "", "Legacy alias for `REJECTED`. Deprecated.", "", "Legacy alias for `DRAFT`. Deprecated."], "type": "string"}, "securityAnimation": {"$ref": "SecurityAnimation", "description": "Optional information about the security animation. If this is set a security animation will be rendered on pass details."}, "textModulesData": {"description": "Text module data. If text module data is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from the object and 10 from the class.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "transitOperatorName": {"$ref": "LocalizedString", "description": "The name of the transit operator."}, "transitType": {"description": "Required. The type of transit this class represents, such as \"bus\".", "enum": ["TRANSIT_TYPE_UNSPECIFIED", "BUS", "bus", "RAIL", "rail", "TRAM", "tram", "FERRY", "ferry", "OTHER", "other"], "enumDeprecated": [false, false, true, false, true, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `<PERSON><PERSON>`. Deprecated.", "", "Legacy alias for `RAIL`. Deprecated.", "", "Legacy alias for `TRA<PERSON>`. Deprecated.", "", "Legacy alias for `FERRY`. Deprecated.", "", "Legacy alias for `OTH<PERSON>`. Deprecated."], "type": "string"}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the class. For a pass only ten will be displayed, prioritizing those from the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "version": {"deprecated": true, "description": "Deprecated", "format": "int64", "type": "string"}, "viewUnlockRequirement": {"description": "View Unlock Requirement options for the transit ticket.", "enum": ["VIEW_UNLOCK_REQUIREMENT_UNSPECIFIED", "UNLOCK_NOT_REQUIRED", "UNLOCK_REQUIRED_TO_VIEW"], "enumDescriptions": ["Default value, same as UNLOCK_NOT_REQUIRED.", "Default behavior for all the existing Passes if ViewUnlockRequirement is not set.", "Requires the user to unlock their device each time the pass is viewed. If the user removes their device lock after saving the pass, then they will be prompted to create a device lock before the pass can be viewed."], "type": "string"}, "watermark": {"$ref": "Image", "description": "Watermark image to display on the user's device."}, "wideLogo": {"$ref": "Image", "description": "The wide logo of the ticket. When provided, this will be used in place of the logo in the top left of the card view."}, "wordMark": {"$ref": "Image", "deprecated": true, "description": "Deprecated."}}, "type": "object"}, "TransitClassAddMessageResponse": {"id": "TransitClassAddMessageResponse", "properties": {"resource": {"$ref": "TransitClass", "description": "The updated TransitClass resource."}}, "type": "object"}, "TransitClassListResponse": {"id": "TransitClassListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "TransitClass"}, "type": "array"}}, "type": "object"}, "TransitObject": {"id": "TransitObject", "properties": {"activationStatus": {"$ref": "ActivationStatus", "description": "The activation status for the object. Required if the class has `activationOptions` set."}, "appLinkData": {"$ref": "AppLinkData", "description": "Optional app or website link that will be displayed as a button on the front of the pass. If AppLinkData is provided for the corresponding class only object AppLinkData will be displayed."}, "barcode": {"$ref": "Barcode", "description": "The barcode type and value."}, "classId": {"description": "Required. The class associated with this object. The class must be of the same type as this object, must already exist, and must be approved. Class IDs should follow the format issuer ID.identifier where the former is issued by Google and latter is chosen by you.", "type": "string"}, "classReference": {"$ref": "TransitClass", "description": "A copy of the inherited fields of the parent class. These fields are retrieved during a GET."}, "concessionCategory": {"description": "The concession category for the ticket.", "enum": ["CONCESSION_CATEGORY_UNSPECIFIED", "ADULT", "adult", "CHILD", "child", "SENIOR", "senior"], "enumDeprecated": [false, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `ADULT`. Deprecated.", "", "Legacy alias for `CHILD`. Deprecated.", "", "Legacy alias for `SENIOR`. Deprecated."], "type": "string"}, "customConcessionCategory": {"$ref": "LocalizedString", "description": "A custom concession category to use when `concessionCategory` does not provide the right option. Both `concessionCategory` and `customConcessionCategory` may not be set."}, "customTicketStatus": {"$ref": "LocalizedString", "description": "A custom status to use for the ticket status value when `ticketStatus` does not provide the right option. Both `ticketStatus` and `customTicketStatus` may not be set."}, "deviceContext": {"$ref": "DeviceContext", "description": "Device context associated with the object."}, "disableExpirationNotification": {"description": "Indicates if notifications should explicitly be suppressed. If this field is set to true, regardless of the `messages` field, expiration notifications to the user will be suppressed. By default, this field is set to false. Currently, this can only be set for offers.", "type": "boolean"}, "groupingInfo": {"$ref": "GroupingInfo", "description": "Information that controls how passes are grouped together."}, "hasLinkedDevice": {"description": "Whether this object is currently linked to a single device. This field is set by the platform when a user saves the object, linking it to their device. Intended for use by select partners. Contact support for additional information.", "type": "boolean"}, "hasUsers": {"description": "Indicates if the object has users. This field is set by the platform.", "type": "boolean"}, "heroImage": {"$ref": "Image", "description": "Optional banner image displayed on the front of the card. If none is present, hero image of the class, if present, will be displayed. If hero image of the class is also not present, nothing will be displayed."}, "hexBackgroundColor": {"description": "The background color for the card. If not set the dominant color of the hero image is used, and if no hero image is set, the dominant color of the logo is used. The format is #rrggbb where rrggbb is a hex RGB triplet, such as `#ffcc00`. You can also use the shorthand version of the RGB triplet which is #rgb, such as `#fc0`.", "type": "string"}, "id": {"description": "Required. The unique identifier for an object. This ID must be unique across all objects from an issuer. This value should follow the format issuer ID.identifier where the former is issued by Google and latter is chosen by you. The unique identifier should only include alphanumeric characters, '.', '_', or '-'.", "type": "string"}, "imageModulesData": {"description": "Image module data. The maximum number of these fields displayed is 1 from object level and 1 for class object level.", "items": {"$ref": "ImageModuleData"}, "type": "array"}, "infoModuleData": {"$ref": "InfoModuleData", "description": "Deprecated. Use textModulesData instead."}, "linkedObjectIds": {"description": "linked_object_ids are a list of other objects such as event ticket, loyalty, offer, generic, giftcard, transit and boarding pass that should be automatically attached to this transit object. If a user had saved this transit card, then these linked_object_ids would be automatically pushed to the user's wallet (unless they turned off the setting to receive such linked passes). Make sure that objects present in linked_object_ids are already inserted - if not, calls would fail. Once linked, the linked objects cannot be unlinked. You cannot link objects belonging to another issuer. There is a limit to the number of objects that can be linked to a single object. After the limit is reached, new linked objects in the call will be ignored silently. Object IDs should follow the format issuer ID. identifier where the former is issued by Google and the latter is chosen by you.", "items": {"type": "string"}, "type": "array"}, "linksModuleData": {"$ref": "LinksModuleData", "description": "Links module data. If links module data is also defined on the class, both will be displayed."}, "locations": {"deprecated": true, "description": "Note: This field is currently not supported to trigger geo notifications.", "items": {"$ref": "LatLongPoint"}, "type": "array"}, "merchantLocations": {"description": "Merchant locations. There is a maximum of ten on the object. Any additional MerchantLocations added beyond the 10 will be rejected. These locations will trigger a notification when a user enters within a Google-set radius of the point. This field replaces the deprecated LatLongPoints.", "items": {"$ref": "MerchantLocation"}, "type": "array"}, "messages": {"description": "An array of messages displayed in the app. All users of this object will receive its associated messages. The maximum number of these fields is 10.", "items": {"$ref": "Message"}, "type": "array"}, "notifyPreference": {"description": "Whether or not field updates to this object should trigger notifications. When set to NOTIFY, we will attempt to trigger a field update notification to users. These notifications will only be sent to users if the field is part of an allowlist. If set to DO_NOT_NOTIFY or NOTIFICATION_SETTINGS_UNSPECIFIED, no notification will be triggered. This setting is ephemeral and needs to be set with each PATCH or UPDATE request, otherwise a notification will not be triggered.", "enum": ["NOTIFICATION_SETTINGS_FOR_UPDATES_UNSPECIFIED", "NOTIFY_ON_UPDATE"], "enumDescriptions": ["Default behavior is no notifications sent.", "This value will result in a notification being sent, if the updated fields are part of an allowlist."], "type": "string"}, "passConstraints": {"$ref": "PassConstraints", "description": "Pass constraints for the object. Includes limiting NFC and screenshot behaviors."}, "passengerNames": {"description": "The name(s) of the passengers the ticket is assigned to. The above `passengerType` field is meant to give Google context on this field.", "type": "string"}, "passengerType": {"description": "The number of passengers.", "enum": ["PASSENGER_TYPE_UNSPECIFIED", "SINGLE_PASSENGER", "single<PERSON><PERSON><PERSON>er", "MULTIPLE_PASSENGERS", "multiplePassengers"], "enumDeprecated": [false, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `SINGLE_PASSENGER`. Deprecated.", "", "Legacy alias for `MULTIP<PERSON>_PASSENGERS`. Deprecated."], "type": "string"}, "purchaseDetails": {"$ref": "PurchaseDetails", "description": "Purchase details for this ticket."}, "rotatingBarcode": {"$ref": "RotatingBarcode", "description": "The rotating barcode type and value."}, "saveRestrictions": {"$ref": "SaveRestrictions", "description": "Restrictions on the object that needs to be verified before the user tries to save the pass. Note that this restrictions will only be applied during save time. If the restrictions changed after a user saves the pass, the new restrictions will not be applied to an already saved pass."}, "smartTapRedemptionValue": {"description": "The value that will be transmitted to a Smart Tap certified terminal over NFC for this object. The class level fields `enableSmartTap` and `redemptionIssuers` must also be set up correctly in order for the pass to support Smart Tap. Only ASCII characters are supported.", "type": "string"}, "state": {"description": "Required. The state of the object. This field is used to determine how an object is displayed in the app. For example, an `inactive` object is moved to the \"Expired passes\" section.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "active", "COMPLETED", "completed", "EXPIRED", "expired", "INACTIVE", "inactive"], "enumDeprecated": [false, false, true, false, true, false, true, false, true], "enumDescriptions": ["Default value.", "Object is active and displayed to with other active objects.", "Legacy alias for `ACTIVE`. Deprecated.", "Object has completed it's lifecycle.", "Legacy alias for `COMPLETED`. Deprecated.", "Object is no longer valid (`validTimeInterval` passed).", "Legacy alias for `EXPIRED`. Deprecated.", "Object is no longer valid", "Legacy alias for `INACTIVE`. Deprecated."], "type": "string"}, "textModulesData": {"description": "Text module data. If text module data is also defined on the class, both will be displayed. The maximum number of these fields displayed is 10 from the object and 10 from the class.", "items": {"$ref": "TextModuleData"}, "type": "array"}, "ticketLeg": {"$ref": "TicketLeg", "description": "A single ticket leg contains departure and arrival information along with boarding and seating information. If more than one leg is to be specified then use the `ticketLegs` field instead. Both `ticketLeg` and `ticketLegs` may not be set."}, "ticketLegs": {"description": "Each ticket may contain one or more legs. Each leg contains departure and arrival information along with boarding and seating information. If only one leg is to be specified then use the `ticketLeg` field instead. Both `ticketLeg` and `ticketLegs` may not be set.", "items": {"$ref": "TicketLeg"}, "type": "array"}, "ticketNumber": {"description": "The number of the ticket. This is a unique identifier for the ticket in the transit operator's system.", "type": "string"}, "ticketRestrictions": {"$ref": "TicketRestrictions", "description": "Information about what kind of restrictions there are on using this ticket. For example, which days of the week it must be used, or which routes are allowed to be taken."}, "ticketStatus": {"description": "The status of the ticket. For states which affect display, use the `state` field instead.", "enum": ["TICKET_STATUS_UNSPECIFIED", "USED", "used", "REFUNDED", "refunded", "EXCHANGED", "exchanged"], "enumDeprecated": [false, false, true, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `USED`. Deprecated.", "", "Legacy alias for `REFUNDED`. Deprecated.", "", "Legacy alias for `EXCHANGED`. Deprecated."], "type": "string"}, "tripId": {"description": "This id is used to group tickets together if the user has saved multiple tickets for the same trip.", "type": "string"}, "tripType": {"description": "Required. The type of trip this transit object represents. Used to determine the pass title and/or which symbol to use between the origin and destination.", "enum": ["TRIP_TYPE_UNSPECIFIED", "ROUND_TRIP", "roundTrip", "ONE_WAY", "oneWay"], "enumDeprecated": [false, false, true, false, true], "enumDescriptions": ["", "", "Legacy alias for `ROUND_TRIP`. Deprecated.", "", "Legacy alias for `ONE_WAY`. Deprecated."], "type": "string"}, "validTimeInterval": {"$ref": "TimeInterval", "description": "The time period this object will be `active` and object can be used. An object's state will be changed to `expired` when this time period has passed."}, "valueAddedModuleData": {"description": "Optional value added module data. Maximum of ten on the object.", "items": {"$ref": "ValueAddedModuleData"}, "type": "array"}, "version": {"deprecated": true, "description": "Deprecated", "format": "int64", "type": "string"}}, "type": "object"}, "TransitObjectAddMessageResponse": {"id": "TransitObjectAddMessageResponse", "properties": {"resource": {"$ref": "TransitObject", "description": "The updated TransitObject resource."}}, "type": "object"}, "TransitObjectListResponse": {"id": "TransitObjectListResponse", "properties": {"pagination": {"$ref": "Pagination", "description": "Pagination of the response."}, "resources": {"description": "Resources corresponding to the list request.", "items": {"$ref": "TransitObject"}, "type": "array"}}, "type": "object"}, "TransitObjectUploadRotatingBarcodeValuesRequest": {"description": "Request to upload rotating barcode values.", "id": "TransitObjectUploadRotatingBarcodeValuesRequest", "properties": {"blob": {"$ref": "Media", "description": "A reference to the rotating barcode values payload that was uploaded."}, "mediaRequestInfo": {"$ref": "MediaRequestInfo", "description": "Extra information about the uploaded media."}}, "type": "object"}, "TransitObjectUploadRotatingBarcodeValuesResponse": {"description": "Response for uploading rotating barcode values.", "id": "TransitObjectUploadRotatingBarcodeValuesResponse", "properties": {}, "type": "object"}, "TranslatedString": {"id": "TranslatedString", "properties": {"kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#translatedString\"`.", "type": "string"}, "language": {"description": "Represents the BCP 47 language tag. Example values are \"en-US\", \"en-GB\", \"de\", or \"de-AT\".", "type": "string"}, "value": {"description": "The UTF-8 encoded translated string.", "type": "string"}}, "type": "object"}, "UpcomingNotification": {"description": "Indicates that the issuer would like Google Wallet to send an upcoming card validity notification 1 day before card becomes valid/usable.", "id": "UpcomingNotification", "properties": {"enableNotification": {"description": "Indicates if the object needs to have upcoming notification enabled.", "type": "boolean"}}, "type": "object"}, "Uri": {"id": "<PERSON><PERSON>", "properties": {"description": {"description": "The URI's title appearing in the app as text. Recommended maximum is 20 characters to ensure full string is displayed on smaller screens. Note that in some contexts this text is not used, such as when `description` is part of an image.", "type": "string"}, "id": {"description": "The ID associated with a uri. This field is here to enable ease of management of uris.", "type": "string"}, "kind": {"deprecated": true, "description": "Identifies what kind of resource this is. Value: the fixed string `\"walletobjects#uri\"`.", "type": "string"}, "localizedDescription": {"$ref": "LocalizedString", "description": "Translated strings for the description. Recommended maximum is 20 characters to ensure full string is displayed on smaller screens."}, "uri": {"description": "The location of a web page, image, or other resource. URIs in the `LinksModuleData` module can have different prefixes indicating the type of URI (a link to a web page, a link to a map, a telephone number, or an email address). URIs must have a scheme.", "type": "string"}}, "type": "object"}, "ValueAddedModuleData": {"description": "Data for Value Added module. Required fields are header and uri.", "id": "ValueAddedModuleData", "properties": {"body": {"$ref": "LocalizedString", "description": "Body to be displayed on the module. Character limit is 50 and longer strings will be truncated."}, "header": {"$ref": "LocalizedString", "description": "Header to be displayed on the module. Character limit is 60 and longer strings will be truncated."}, "image": {"$ref": "Image", "description": "Image to be displayed on the module. Recommended image ratio is 1:1. Images will be resized to fit this ratio."}, "sortIndex": {"description": "The index for sorting the modules. Modules with a lower sort index are shown before modules with a higher sort index. If unspecified, the sort index is assumed to be INT_MAX. For two modules with the same index, the sorting behavior is undefined.", "format": "int32", "type": "integer"}, "uri": {"description": "URI that the module leads to on click. This can be a web link or a deep link as mentioned in https://developer.android.com/training/app-links/deep-linking.", "type": "string"}, "viewConstraints": {"$ref": "ModuleViewConstraints", "description": "Constraints that all must be met for the module to be shown."}}, "type": "object"}}, "servicePath": "", "title": "Google Wallet API", "version": "v1"}