Metadata-Version: 2.1
Name: google-auth-oauthlib
Version: 1.2.2
Summary: Google Authentication Library
Home-page: https://github.com/GoogleCloudPlatform/google-auth-library-python-oauthlib
Author: Google Cloud Platform
Author-email: <EMAIL>
License: Apache 2.0
Keywords: google auth oauth client oauthlib
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: POSIX
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: OS Independent
Classifier: Topic :: Internet :: WWW/HTTP
Requires-Python: >=3.6
License-File: LICENSE
Requires-Dist: google-auth>=2.15.0
Requires-Dist: requests-oauthlib>=0.7.0
Provides-Extra: tool
Requires-Dist: click>=6.0.0; extra == "tool"

oauthlib integration for Google Auth
====================================

|pypi|

This library provides `oauthlib`_ integration with `google-auth`_.

.. |build| image:: https://travis-ci.org/googleapis/google-auth-library-python-oauthlib.svg?branch=main
   :target: https://googleapis.dev/python/google-auth-oauthlib/latest/index.html
.. |pypi| image:: https://img.shields.io/pypi/v/google-auth-oauthlib.svg
   :target: https://pypi.python.org/pypi/google-auth-oauthlib

.. _oauthlib: https://github.com/idan/oauthlib
.. _google-auth: https://github.com/googleapis/google-auth-library-python

Installing
----------

You can install using `pip`_::

    $ pip install google-auth-oauthlib

.. _pip: https://pip.pypa.io/en/stable/

Documentation
-------------

The latest documentation is available at `google-auth-oauthlib.googleapis.dev`_.

.. _google-auth-oauthlib.googleapis.dev: https://googleapis.dev/python/google-auth-oauthlib/latest/index.html

Supported Python Versions
-------------------------
Python >= 3.6


Unsupported Python Versions
---------------------------

Python == 2.7, Python == 3.5.

The last version of this library compatible with Python 2.7 and 3.5 is
`google-auth-oauthlib==0.4.1`.

License
-------

Apache 2.0 - See `the LICENSE`_ for more information.

.. _the LICENSE: https://github.com/googleapis/google-auth-library-python-oauthlib/blob/main/LICENSE
