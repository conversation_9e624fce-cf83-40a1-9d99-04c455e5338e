import os
from datetime import datetime
from dotenv import load_dotenv
import anthropic
from main import (
    authenticate_gmail, 
    fetch_newsletters, 
    extract_news_items,
    score_news_item,
    enhance_news_items
)
import prompts

load_dotenv()

def test_single_newsletter():
    """Test processing on a single newsletter"""
    
    # Initialize
    print("🔧 Setting up test environment...")
    service = authenticate_gmail()
    client = anthropic.Anthropic(api_key=os.getenv('ANTHROPIC_API_KEY'))
    
    # Fetch newsletters
    newsletters = fetch_newsletters(service, hours_back=48)  # Last 2 days
    
    if not newsletters:
        print("❌ No newsletters found")
        return
    
    # Show available newsletters
    print(f"\n📧 Found {len(newsletters)} newsletters:")
    for i, nl in enumerate(newsletters):
        print(f"{i+1}. {nl['sender']} - {nl['subject'][:60]}...")
    
    # Select one
    choice = input("\nWhich newsletter to test? (number): ")
    newsletter = newsletters[int(choice) - 1]
    
    print(f"\n🧪 Testing newsletter from: {newsletter['sender']}")
    print(f"Subject: {newsletter['subject']}")
    print(f"Body preview: {newsletter['body'][:200]}...\n")
    
    # Test extraction
    print("📤 Extracting news items...")
    items = extract_news_items(newsletter, client)
    print(f"✅ Found {len(items)} items\n")
    
    # Show and score each item
    scored_items = []
    for i, item in enumerate(items, 1):
        print(f"\n{'='*60}")
        print(f"ITEM {i}:")
        print(f"Content: {item[:300]}...")
        
        # Score it
        scored = score_news_item(item, newsletter['sender'], client)
        scored_items.append(scored)
        
        print(f"\n📊 SCORING:")
        print(f"Score: {scored['score']}/10")
        print(f"Category: {scored['category']}")
        print(f"Entities: {', '.join(scored['key_entities'])}")
        print(f"Summary: {scored['summary']}")
    
    # Test enhancement on top item
    if scored_items:
        top_item = max(scored_items, key=lambda x: x['score'])
        print(f"\n{'='*60}")
        print("🧠 TESTING ENHANCEMENT on highest scored item:")
        
        # Enhance it
        enhanced = enhance_news_items([{
            'category': top_item['category'],
            'summary': top_item['summary'],
            'score': top_item['score']
        }], client)[0]
        
        print(f"\n💡 Why this matters: {enhanced.get('why_matters', 'N/A')}")
        if enhanced.get('tech_notes'):
            print(f"🔧 Technical notes: {enhanced['tech_notes']}")
    
    print("\n✅ Test complete!")

if __name__ == "__main__":
    test_single_newsletter()